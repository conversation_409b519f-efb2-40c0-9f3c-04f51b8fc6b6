<hc-form-field *ngIf="!isReadOnly">
  <input (dateChange)="saveChanges($event)" hcInput [hcDatepicker]="picker" (click)="picker.open()" placeholder="mm/dd/yyyy" [max]="maxStr" [value]="response.responseText">
  <hc-datepicker-toggle hcSuffix [for]="picker">
    <hc-icon hcDatepickerToggleIcon><i class="glyphicon glyphicon-calendar calendar-icon blue"></i></hc-icon>
  </hc-datepicker-toggle>
  <hc-datepicker #picker></hc-datepicker>

</hc-form-field>
<div *ngIf="!isReadOnly">
  (<a class="hc-link" (click)="onQuestionClearEvent()">clear</a>)
</div>


<div *ngIf="isReadOnly && response.responseText !== null">{{response.responseText | datex :'MM/DD/YYYY'}}</div>
<div *ngIf="isReadOnly && response.responseText === null">--</div>

