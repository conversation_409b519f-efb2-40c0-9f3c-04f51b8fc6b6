import { Component, OnInit, Input, OnChanges } from '@angular/core';
import { PositiveTrigger } from 'app/shared/models/positive-trigger';
import { TriggerFlags } from 'app/shared/models/trigger-flags';

@Component({
  selector: 'landing-nlp-template',
  templateUrl: './landing-nlp-template.component.html',
  styleUrls: ['./landing-nlp-template.component.scss']
})
export class LandingNlpTemplateComponent implements OnInit, OnChanges {


  @Input()
  trigger: PositiveTrigger;
  triggerDscCondensed: string;
  triggerFlag: TriggerFlags;
  iconPath: string;

  constructor() {
    this.iconPath = 'assets/images/psm_alert.png';
   }

  ngOnInit() {
    this.triggerFlag = this.trigger.triggerFlags ? this.trigger.triggerFlags[0] : null;
    const triggerDsc = this.trigger.triggerDsc;
    if (triggerDsc) {
      if (triggerDsc.length > 150) {
        this.triggerDscCondensed = triggerDsc.substring(0, 149) + '...';
      } else {
        this.triggerDscCondensed = triggerDsc;
      }
    }
  }

  ngOnChanges(): void {
    this.triggerFlag = this.trigger.triggerFlags ? this.trigger.triggerFlags[0] : null;
  }

}
