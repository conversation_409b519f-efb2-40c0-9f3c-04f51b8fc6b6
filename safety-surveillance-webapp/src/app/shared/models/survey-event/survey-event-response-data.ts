import { Provider } from "../provider";
import { Device } from "../device";
import { LocationOption } from "../location-option";

export class SurveyEventResponseData {
    responseId: string;
    surveyEventId: string;
    questionId: string;
    isDeleted: boolean;
    responseOptionId: string;
    responseText: string;
    responseTextFromOption: string;
    responseMultiSelectTypeId: number;
    deviceResponses: Device[];
    providerResponses: Provider [];
    additionalText: string;
    associatedPersonResponse: any;
    isPreloaded: boolean;
    location: LocationOption;
    responseOption: any;
    facility: string;
}
