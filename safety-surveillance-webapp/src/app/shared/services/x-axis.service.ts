import { HacType } from 'app/shared/constants/hac-type';
import { XAxisType } from 'app/shared/models/public-health/x-axis-type';
import { Injectable } from '@angular/core';
import * as moment from 'moment';

@Injectable()
export class XAxisService {
  private cautiXAxisType: string = XAxisType[2];

  private clabsiXAxisType: string = XAxisType[2];

  private pressureInjuryXAxisType: string = XAxisType[2];

  private febrileNeutropeniaXAxisType: string = XAxisType[2];

  setXAxisType(typeSelection: string, hacType: string) {
    this.setHacTypeXAxisType(hacType, typeSelection);
  }

  getXAxisType(hacType: string) {
    return this.returnHacTypeXAxisType(hacType);
  }

  loadXAxisData(startDT, endDT) {
    let xAxisTypes = [];
    const dateDiff = moment(endDT).diff(moment(startDT), 'days');
    if (dateDiff > 32) {
      xAxisTypes = Object.keys(XAxisType)
        .filter((key) => !isNaN(Number(XAxisType[key])))
        .filter((key) => XAxisType[key] !== XAxisType.Daily);
    } else {
      xAxisTypes = Object.keys(XAxisType)
        .filter((key) => !isNaN(Number(XAxisType[key])))
        .filter((key) => XAxisType[key] !== XAxisType.Monthly);
    }

    return xAxisTypes;
  }

  getXAxisTitle(xAxisSelection: string) {
    if (xAxisSelection === 'Weekly') {
      return 'Week of';
    }
    return '';
  }

  setDateChangeXAxis(hacType: string, startDT, endDT) {

    const dateDiff = moment(endDT).diff(moment(startDT), 'days');
    let selectedXAxisType = this.returnHacTypeXAxisType(hacType);

    if (dateDiff > 32) {
      if (selectedXAxisType === XAxisType[0]) {
        selectedXAxisType = XAxisType[2];
      }
    } else {
      if (selectedXAxisType === XAxisType[2]) {
        selectedXAxisType = XAxisType[0];
      }
    }

    this.setHacTypeXAxisType(hacType, selectedXAxisType);

    return selectedXAxisType;
  }

  setGridBottomPercent(selectedXAxisType: string, percent: string = '30%') {
    if (selectedXAxisType === 'Monthly') {
      return '';
    } else {
      return percent;
    }
  }

  private returnHacTypeXAxisType(hacType: string) {
    switch (hacType) {
      case HacType.Cauti:
        return this.cautiXAxisType;
      case HacType.Clabsi:
        return this.clabsiXAxisType;
      case HacType.FebrileNeutropenia:
        return this.febrileNeutropeniaXAxisType;
      case HacType.PressureInjury:
        return this.pressureInjuryXAxisType;
    }
  }

  private setHacTypeXAxisType(hacType: string, type: string) {
    switch (hacType) {
      case HacType.Cauti:
        this.cautiXAxisType = type;
        break;
      case HacType.Clabsi:
        this.clabsiXAxisType = type;
        break;
      case HacType.FebrileNeutropenia:
        this.febrileNeutropeniaXAxisType = type;
        break;
      case HacType.PressureInjury:
        this.pressureInjuryXAxisType = type;
        break;
    }
  }
}
