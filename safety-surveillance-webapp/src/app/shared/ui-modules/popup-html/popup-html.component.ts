import { Component, OnInit, ViewEncapsulation, Input } from '@angular/core';

@Component({
  selector: 'popup-html',
  templateUrl: './popup-html.component.html',
  styleUrls: ['./popup-html.component.scss'],
})
export class PopupHtmlComponent implements OnInit {

  @Input()
  public imagePath: string;

  @Input()
  public html: string;

  constructor() { }

  ngOnInit() {
    if (!this.imagePath) {
      this.imagePath = 'assets/images/question.png';
    }
  }

}
