import { FilterOption } from 'app/shared/filter/filter-option';
import { TriggerCategories } from './../../../../models/filters-object';
import { TriggerService } from './../../../../services/trigger.service';
import { SimpleModalService } from 'ngx-simple-modal';
import { AdverseEventFilterCreatorUtil } from './../adverse-event-filter-creator-util';
import { PatientGrouping } from '../../../../models/patient-grouping';
import { FilterChangeService } from '../../../filter-change-service';
import { LocalStorageService } from '../../../../services/local-storage.service';
import { LocalStorageCategory } from '../../../../constants/local-storage-category';
import { Injectable } from '@angular/core';
import { IFilter } from '../../../iFilter';
import { TriggerCategory } from 'app/shared/constants/trigger-category';
import { TriggerReference } from 'app/shared/models/trigger-reference';
import { CoagulationFilterModalComponent } from 'app/shared/filter/triggers/filters/coagulation-filter/coagulation-filter-modal/coagulation-filter-modal.component';
import { SharedFilterService } from 'app/shared/ui-modules/shared-filter/shared-filter-service';

@Injectable()
export class AdverseEventsCoagulationFilter implements IFilter<PatientGrouping> {
  id: number;
  type: string;
  isVisible: boolean;
  label: string;
  count = 0;
  triggerType = 'Coagulation';
  availableItems: TriggerReference[];
  filteredOptions: TriggerReference[];
  triggerOptions: TriggerReference[];
  localStorageCategoryName = LocalStorageCategory.adverseEventFilterLocalStorage;

  constructor(
    private dialogService: SimpleModalService,
    private sharedFilterService: SharedFilterService,
    private filterChangeService: FilterChangeService,
    private localStorageService: LocalStorageService,
    private triggerService: TriggerService,
    private filterCreator: AdverseEventFilterCreatorUtil,
  ) {
    this.label = 'Coagulation Triggers';
    this.type = TriggerCategory.coagulation;
    this.isVisible = true;

    this.filteredOptions = new Array<TriggerReference>();

    this.loadSavedOptions();

    this.triggerService.getTriggerReferences(this.type).subscribe(data => {
      this.availableItems = data;
      if (this.availableItems.length > 0) {
          this.isVisible = true;
      }
      else {
          this.isVisible = false;
      }
  });
  }

  remoteSet(filterOptions: any[]) {
    this.filteredOptions = filterOptions;

    this.setTriggerFilter();
    this.loadSavedOptions();
  }

  loadSavedOptions() {
    const savedTrigger = this.localStorageService.getLocalStorage(this.localStorageCategoryName);

    if (savedTrigger != null) {
      const json = JSON.parse(savedTrigger);

      const returned = this.filterCreator.loadSavedTriggerOptions(json, this.triggerType);
      this.count = returned.count;
      this.filteredOptions = returned.filtered;
    }
  }

  filterData(itemsToFilter: PatientGrouping[]): PatientGrouping[] {

    if (this.filteredOptions !== undefined && this.filteredOptions.length > 0) {
      const selectedValues = this.filteredOptions.map(({ triggerId }) => triggerId);
      const filteredList = new Array<PatientGrouping>();

      itemsToFilter.forEach(element => {
        element.triggers.forEach(positiveTrigger => {
          if (selectedValues.indexOf(positiveTrigger.triggerId) > -1) {
            filteredList.push(element);
          }
        })
      });

      return filteredList;
    }

    return itemsToFilter;
  }

  public clearFilter(silent = false) {
    this.count = 0;
    this.filteredOptions = [];
    let savedOptions = JSON.parse(this.localStorageService.getLocalStorage(this.localStorageCategoryName));
    savedOptions = this.filterCreator.clearTriggerFilter(savedOptions, this.triggerType);

    this.localStorageService.setLocalStorage(
      this.localStorageCategoryName,
      JSON.stringify(savedOptions)
    );

    if (!silent) {
      this.filterChangeService.notifyChange();
    }
  }

  public showFilterDialog() {
    const disposable = this.dialogService
      .addModal(CoagulationFilterModalComponent, {
        value: null,
        data: this.availableItems,
        localStorage: this.localStorageCategoryName
      })
      .subscribe((result: TriggerReference[]) => {
        if (result === undefined) {
          return;
        }

        this.filteredOptions = result;

        this.setTriggerFilter();
       

        if (this.filteredOptions.length > 0) {
          this.count = this.filteredOptions.length;
        } else {
          this.count = 0;
        }

        this.filterChangeService.notifyChange();
      });
  }

  private setTriggerFilter() {
    let savedOptions;
    const triggerFilter = new TriggerCategories;

    triggerFilter.triggerReferences = new Array<TriggerReference>();
    triggerFilter.triggerName = this.triggerType;

    if (this.localStorageService.getLocalStorage(this.localStorageCategoryName)) {
      savedOptions = JSON.parse(this.localStorageService.getLocalStorage(this.localStorageCategoryName));

      savedOptions = this.filterCreator.clearTriggerFilter(savedOptions, this.triggerType);

    } else {
      savedOptions = this.filterCreator.createNewAdverseEventFilters();
    }

    this.filteredOptions.forEach(data => {
      triggerFilter.triggerReferences.push(data);
    });

    savedOptions.categoryTriggers.push(triggerFilter);

    this.localStorageService.setLocalStorage(this.localStorageCategoryName, JSON.stringify(savedOptions));
  }

  getSelectedFilterOptions(): FilterOption[] {
    throw new Error('Method not implemented.');
  }
}
