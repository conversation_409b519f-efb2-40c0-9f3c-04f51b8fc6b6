import { SimpleModalService } from 'ngx-simple-modal';
import { DateRangeModalComponent } from '../../modals/date-range-modal/date-range-modal.component';
import { LocalStorageService } from '../../services/local-storage.service';
import { DateRangeService } from '../../services/date-range.service';
import { DateInput } from '../../models/date-input';
import { FilterOption } from 'app/shared/filter/filter-option';
import { FilterBarService } from './filter-bar-service';
import { ModalOptions, ModalService, PresetItem } from '@healthcatalyst/cashmere';
import { TimeRangeFilterConfig } from '../time-range-filter-config';

export abstract class TimeRangeFilter {
  type: string;
  isVisible: boolean;
  count: number;
  isSelected: boolean;
  label: string;
  options: FilterOption[];
  selectedOption: FilterOption;

  selectedStartDateCategoryName: string;
  selectedEndDateCategoryName: string;

  constructor(
    private dialogService: SimpleModalService,
    private filterService: FilterBarService,
    private localStorageService: LocalStorageService,
    private dateRangeService: DateRangeService,
    private defaultFilter: FilterOption,
    private localStorageCategory: string,
    private localStorageCategoryExpiration: string,
    private showTime: boolean,
    private customPreset: PresetItem[],
    private readonly _modalService: ModalService
  ) {
    this.label = 'Time Frame';
    this.type = 'daterange';
    this.isVisible = true;
    this.init({
      localStorageCategory: localStorageCategory,
      localStorageCategoryExpiration: localStorageCategoryExpiration,
      filterBarConfig: null,
      defaultFilter: defaultFilter,
      showTime: this.showTime,
      customPreset: this.customPreset,
    });
  }

  public init(config: TimeRangeFilterConfig) {
    if (config.filterBarConfig) {
      this.selectedStartDateCategoryName = config.filterBarConfig.startDateLocalStorageName;
      this.selectedEndDateCategoryName = config.filterBarConfig.endDateLocalStorageName;
    }
    this.defaultFilter = config.defaultFilter;
    this.localStorageCategory = config.localStorageCategory;
    this.localStorageCategoryExpiration = config.localStorageCategoryExpiration;
    this.options = new Array<FilterOption>(this.selectedOption);
    const dateString = this.localStorageService.getLocalStorage(this.localStorageCategoryExpiration)
    if (!dateString) {
      const midnight = new Date(new Date().setHours(24, 0, 0, 0));
      this.localStorageService.setLocalStorage(this.localStorageCategoryExpiration, midnight.toString());
    }

    if (this.localStorageService.getLocalStorage(this.localStorageCategory) === null) {
      this.selectedOption = this.defaultFilter;
      this.localStorageService.setLocalStorage(this.localStorageCategory, this.selectedOption.name);
    }
    else {
      const expirationDate = new Date(dateString);
      const now = new Date();

      if (expirationDate < now) {
        this.clearFilter();
        const midnight = new Date(new Date().setHours(24, 0, 0, 0));
        this.localStorageService.setLocalStorage(this.localStorageCategoryExpiration, midnight.toString());
      }

      const savedOptionName = this.localStorageService.getLocalStorage(this.localStorageCategory);
      const savedOption = new FilterOption(savedOptionName, '');
      this.selectedOption = savedOption;
    }
  }

  public showDateRangeDialog(showIncidentDateRangeType = false) {
    const options: ModalOptions = {
      ignoreEscapeKey: true,
      ignoreOverlayClick: true,
      size: 'auto',
      data: {
        showTime: this.showTime,
        selectedStartDateCategoryName: this.selectedStartDateCategoryName,
        selectedEndDateCategoryName: this.selectedEndDateCategoryName,
        customPresets: this.customPreset,
        message: null,
        showIncidentDateRangeType: showIncidentDateRangeType
      }
    };
    const disposable = this._modalService.open(DateRangeModalComponent, options);
    disposable.result.subscribe((result: any, localStorage = this.localStorageCategory) => {
      if (result) {
        this.filterService.setDateRangeInput(result);
        this.selectedOption = new FilterOption(result.label, '');
        this.localStorageService.setLocalStorage(localStorage, this.selectedOption.name);
      }
    });
  }

  public clearFilter(dayFilterOverride?: number) {
    const defaultEndDate = this.dateRangeService.calculateDefaultEndDate();
    let startDate;
    if (dayFilterOverride) {
      startDate = this.dateRangeService.calculateStartDateInDays(dayFilterOverride, defaultEndDate);
    } else {
      startDate = this.dateRangeService.calculateStartDateInDays(this.defaultFilter.value, defaultEndDate);
    }
    const dateInput = new DateInput();
    dateInput.start = startDate;
    dateInput.end = defaultEndDate;
    dateInput.label = this.defaultFilter.name;
    this.selectedOption = new FilterOption(dateInput.label, '');
    this.filterService.setDateRangeInput(dateInput);
    this.localStorageService.setLocalStorage(this.localStorageCategory, this.selectedOption.name);
  }

  public remoteSet(dateInput: DateInput) {
    this.filterService.setDateRangeInput(dateInput);
    this.selectedOption = new FilterOption(dateInput.label, '');
    this.localStorageService.setLocalStorage(this.localStorageCategory, this.selectedOption.name);
  }
}
