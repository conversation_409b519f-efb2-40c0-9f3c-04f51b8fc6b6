// import { async, ComponentFixture, TestBed } from '@angular/core/testing';

// import { FebrileNeutropeniaCountsComponent } from './febrile-neutropenia-counts.component';

// describe('FebrileNeutropeniaCountsComponent', () => {
//   let component: FebrileNeutropeniaCountsComponent;
//   let fixture: ComponentFixture<FebrileNeutropeniaCountsComponent>;

//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ FebrileNeutropeniaCountsComponent ]
//     })
//     .compileComponents();
//   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(FebrileNeutropeniaCountsComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
