.link-incidents-search {
    margin-top: 5px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 3px;
    box-shadow: 0 1px 1px hsl(0deg 0% 0% / 0.075), 0 2px 2px hsl(0deg 0% 0% / 0.075), 0 4px 4px hsl(0deg 0% 0% / 0.075),
        0 8px 8px hsl(0deg 0% 0% / 0.075), 0 16px 16px hsl(0deg 0% 0% / 0.075);
    max-height: 550px;
    display: flex;
    flex-direction: column;

    .search-table {
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }

    .search-content {
        padding: 10px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        flex: 1;
        min-height: 100px;
    }

    &-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 5px;
        padding: 10px;
        border-bottom: 1px solid #ccc;
        flex-shrink: 1;
    }

    &-list {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        overflow-y: auto;

        .result {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            gap: 5px;
            padding: 10px;
            border-bottom: 1px dashed #ccc;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: #f0f0f0;
                cursor: pointer;
                transition: all 250ms ease-in-out;
            }
        }
    }

    .incident-id {
        width: 10%;
    }

    .entity {
        width: 20%;
    }

    .incident-type {
        width: 15%;
    }

    .description {
        width: 25%;

        .inner-html {
            ::ng-deep p {
                font-size: 0.95rem !important;
                margin-bottom: 10px !important;
            }

            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            list-style-position: inside;

            ::ng-deep img {
                display: none;
            }
        }
    }

    .incident-location {
        width: 20%;
    }

    .reported-by {
        width: 10%;
    }

    .anonymous {
        font-style: italic;
    }
}
