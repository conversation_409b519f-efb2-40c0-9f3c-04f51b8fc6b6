<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;osx-x64</RuntimeIdentifiers>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>NU1900;NU1902</WarningsNotAsErrors>
    <LangVersion>11</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IdentityServer4" Version="4.1.2" />
    <PackageReference Include="log4net" Version="2.0.14" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.20.0" />

    <PackageReference Include="Serilog.AspNetCore" Version="3.2.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>
