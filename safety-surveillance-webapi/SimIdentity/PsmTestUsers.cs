namespace SimIdentity
{
    using IdentityModel;
    using IdentityServer4.Test;
    using System.Collections.Generic;
    using System.Security.Claims;

    public class PsmTestUsers
    {
        private const string UserGroup = "HQCATALYST\\Patient Safety";

        private const string ViewerGroup = "TESTIDENTITY\\Viewer";

        private const string PublisherGroup = "TESTIDENTITY\\Publisher";

        private const string AdministratorGroup = "hqcatalyst\\Domain Users";

        public static List<TestUser> Users
        {
            get
            {
                return new()
                {
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\administrator",
                        Username = "administrator",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Administrator"),
                            new Claim(JwtClaimTypes.GivenName, "Admin"),
                            new Claim(JwtClaimTypes.FamilyName, "Person"),
                            new Claim(JwtClaimTypes.NickName, "Yoda Person"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "client\\administrator",
                        Username = "administrator2",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Administrator2"),
                            new Claim(JwtClaimTypes.GivenName, "Admin2"),
                            new Claim(JwtClaimTypes.FamilyName, "Person"),
                            new Claim(JwtClaimTypes.NickName, "Yoda Person"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        Username = "publisher",
                        Password = "asdf",
                        SubjectId = "healthcatalyst.com\\publisher",
                        Claims = new List<Claim>
                        {
                            new(JwtClaimTypes.Name, "Publisher"),
                            new(JwtClaimTypes.GivenName, "Publisher"),
                            new(JwtClaimTypes.FamilyName, "Person"),
                            new(JwtClaimTypes.Email, "<EMAIL>"),
                            new(JwtClaimTypes.Role, UserGroup),
                            new(JwtClaimTypes.Role, PublisherGroup),
                            new(JwtClaimTypes.Role, ViewerGroup)
                        }
                    },
                    new TestUser
                    {
                        Username = "researcher",
                        Password = "asdf",
                        SubjectId = "healthcatalyst.com\\researcher",
                        Claims = new List<Claim>
                        {
                            new(JwtClaimTypes.Name, "Researcher"), new(JwtClaimTypes.Role, UserGroup)
                        }
                    },
                    new TestUser
                    {
                        Username = "unauthorized",
                        Password = "asdf",
                        SubjectId = "healthcatalyst.com\\unauthorized",
                        Claims = new List<Claim> {new(JwtClaimTypes.Name, "Unauthorized")}
                    },
                    new TestUser
                    {
                        Username = "kismet",
                        Password = "asdf",
                        SubjectId = "healthcatalyst.com\\kismet",
                        Claims = new List<Claim> {new(JwtClaimTypes.Name, "Kismet")}
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\Matt1",
                        Username = "Matt1",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Matt1"),
                            new Claim(JwtClaimTypes.GivenName, "Matt"),
                            new Claim(JwtClaimTypes.FamilyName, "Test1"),
                            new Claim(JwtClaimTypes.NickName, "Matt Test1"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\MattZBulk1",
                        Username = "MattZBulk1",
                        Password = "asdf",
                        Claims =
                        {
                        new Claim(JwtClaimTypes.Name, "MattZBulk1"),
                        new Claim(JwtClaimTypes.GivenName, "MattZ"),
                        new Claim(JwtClaimTypes.FamilyName, "Bulk1"),
                        new Claim(JwtClaimTypes.NickName, "Matt Test1"),
                        new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                        new Claim(JwtClaimTypes.Role, UserGroup),
                        new Claim(JwtClaimTypes.Role, PublisherGroup),
                        new Claim(JwtClaimTypes.Role, ViewerGroup),
                        new Claim(JwtClaimTypes.Role, AdministratorGroup),
                        new Claim(JwtClaimTypes.Role, "Everyone"),
                    }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\Shannon1",
                        Username = "Shannon1",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Shannon1"),
                            new Claim(JwtClaimTypes.GivenName, "Shannon"),
                            new Claim(JwtClaimTypes.FamilyName, "Test1"),
                            new Claim(JwtClaimTypes.NickName, "Shannon1"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\Sherrie1",
                        Username = "Sherrie1",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Sherrie1"),
                            new Claim(JwtClaimTypes.GivenName, "Sherrie"),
                            new Claim(JwtClaimTypes.FamilyName, "Test1"),
                            new Claim(JwtClaimTypes.NickName, "Sherrie1"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\harry.potter",
                        Username = "harry.potter",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "harry.potter"),
                            new Claim(JwtClaimTypes.GivenName, "Haroldo"),
                            new Claim(JwtClaimTypes.FamilyName, "Potter"),
                            new Claim(JwtClaimTypes.NickName, "Harry"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\Matt2",
                        Username = "Matt2",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Matt2"),
                            new Claim(JwtClaimTypes.GivenName, "Matt"),
                            new Claim(JwtClaimTypes.FamilyName, "Test2"),
                            new Claim(JwtClaimTypes.NickName, "Matt Test2"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\Matt3",
                        Username = "Matt3",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Matt3"),
                            new Claim(JwtClaimTypes.GivenName, "Matt"),
                            new Claim(JwtClaimTypes.FamilyName, "Test3"),
                            new Claim(JwtClaimTypes.NickName, "Matt Test3"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\Matt4",
                        Username = "Matt4",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Matt4"),
                            new Claim(JwtClaimTypes.GivenName, "Matt"),
                            new Claim(JwtClaimTypes.FamilyName, "Test4"),
                            new Claim(JwtClaimTypes.NickName, "Matt Test4"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "healthcatalyst.com\\Matt5",
                        Username = "Matt5",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "Matt5"),
                            new Claim(JwtClaimTypes.GivenName, "Matt"),
                            new Claim(JwtClaimTypes.FamilyName, "Test5"),
                            new Claim(JwtClaimTypes.NickName, "Matt Test5"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },

                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user1",
                        Username = "user1-pen",
                        Password = "P$Monitor1",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest1"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test1"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test1"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user2",
                        Username = "user2-pen",
                        Password = "P$Monitor2",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest2"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test2"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test2"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user3",
                        Username = "user3-pen",
                        Password = "P$Monitor3",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest3"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test3"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test3"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user4",
                        Username = "user4-pen",
                        Password = "P$Monitor4",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest4"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test4"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test4"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user5",
                        Username = "user5-pen",
                        Password = "P$Monitor5",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest5"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test5"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test5"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user6",
                        Username = "user6-pen",
                        Password = "P$Monitor6",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest6"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test6"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test6"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user7",
                        Username = "user7-pen",
                        Password = "P$Monitor7",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest7"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test7"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test7"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "sysnopsys.com\\user8",
                        Username = "user8-pen",
                        Password = "P$Monitor8",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "PenTest8"),
                            new Claim(JwtClaimTypes.GivenName, "PenTest"),
                            new Claim(JwtClaimTypes.FamilyName, "Test8"),
                            new Claim(JwtClaimTypes.NickName, "Pen Test8"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    },
                    new TestUser
                    {
                        SubjectId = "newuser.com\\first_time_user",
                        Username = "new.user",
                        Password = "asdf",
                        Claims =
                        {
                            new Claim(JwtClaimTypes.Name, "new.user"),
                            new Claim(JwtClaimTypes.GivenName, "New"),
                            new Claim(JwtClaimTypes.FamilyName, "User"),
                            new Claim(JwtClaimTypes.NickName, "New User"),
                            new Claim(JwtClaimTypes.Email, "<EMAIL>"),
                            new Claim(JwtClaimTypes.Role, UserGroup),
                            new Claim(JwtClaimTypes.Role, PublisherGroup),
                            new Claim(JwtClaimTypes.Role, ViewerGroup),
                            new Claim(JwtClaimTypes.Role, AdministratorGroup),
                            new Claim(JwtClaimTypes.Role, "Everyone"),
                        }
                    }
                };
            }
        }
    }
}
