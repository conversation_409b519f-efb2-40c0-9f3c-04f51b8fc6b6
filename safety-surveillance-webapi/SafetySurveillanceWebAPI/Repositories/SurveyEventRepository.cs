

using SafetySurveillanceWebApi.Repositories.Authorization;
using SafetySurveillanceWebApi.Services;

namespace SafetySurveillanceWebApi.Repositories;

using Microsoft.EntityFrameworkCore;

using SafetySurveillanceWebApi.Data;
using SafetySurveillanceWebApi.Extensions;
using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.Constants;
using SafetySurveillanceWebApi.Models.Authorize;
using SafetySurveillanceWebApi.Models.Survey;

public class SurveyEventRepository : ISurveyEventRepository
{
    private readonly ISafetySurveillanceApplicationContext context;
    private readonly ITriggerReviewRepository triggerReviewRepository;
    private readonly IApplicationConfigurationRepository configurationRepository;
    private readonly IAuthorizationRepository authorizationRepository;
    private readonly ITriggerMatchService triggerMatchService;
    private readonly ITriggerEventInvestigationDetailRepository triggerEventInvestigationDetailRepository;

    private readonly HashSet<string> formsWithOneFormPerPatient = new HashSet<string>()
    {
        FormName.SkinIntegrityAudit,
        FormName.FebrileNeutropeniaAudit
    };

    public SurveyEventRepository(ISafetySurveillanceApplicationContext context,
        ITriggerReviewRepository triggerReviewRepository, IApplicationConfigurationRepository configurationRepository, IAuthorizationRepository authorizationRepository, ITriggerMatchService triggerMatchService,
        ITriggerEventInvestigationDetailRepository triggerEventInvestigationDetailRepository)
    {
        this.context = context;
        this.triggerReviewRepository = triggerReviewRepository;
        this.configurationRepository = configurationRepository;
        this.authorizationRepository = authorizationRepository;
        this.triggerMatchService = triggerMatchService;
        this.triggerEventInvestigationDetailRepository = triggerEventInvestigationDetailRepository;
    }

    public async Task<List<SurveyEvent>> GetParentSurveyEventsByFormId(Guid formId, DateTime earliestDate)
    {
        var surveyEventQuery = this.context.SurveyEvents
            .Where(s => s.FormId == formId && s.ParentSurveyEventId == null && !s.IsDeleted && s.CreatedDate >= earliestDate)
            .Include(x => x.StatusReference)
            .Include(s => s.Assignments)
            .ThenInclude(a => a.AssignedToUser);

        return await surveyEventQuery.ToListAsync();
    }

    public async Task<List<SurveyEvent>> GetParentSurveyEventsWithResponsesByFormId(Guid formId, string formName, DateTime earliestDate)
    {
        var surveyEvents = await this.context.SurveyEvents
            .Include(se => se.Form)
            .Where(s => s.FormId == formId
                        && s.ParentSurveyEventId == null
                        && !s.IsDeleted
                        && (((s.CreatedDate >= earliestDate)
                             && (!this.formsWithOneFormPerPatient.Contains(s.Form.Name)))
                            || this.formsWithOneFormPerPatient.Contains(FormName.SkinIntegrityAudit)))
            // grab all forms except for certain forms, then only grab the earliest (1 form)
            .Include(s => s.Responses)
            .ThenInclude(response => response.Question)
            .Include(s => s.ChildSurveyEvents)
            .ThenInclude(s => s.Responses)
            .ThenInclude(response => response.Question)
            .Include(s => s.ChildSurveyEvents)
            .ThenInclude(e => e.Form)
            .ThenInclude(f => f.Sections)
            .ThenInclude(s => s.Questions)
            .Include(s => s.StatusReference)
            .Include(s => s.Assignments)
            .ThenInclude(a => a.AssignedToUser)
            .ToListAsync();

        // ModifiedByUser gets NULLed out by constructor of SurveyEvent during .Include() method
        List<SafetyUser> allSafetyUsers = await this.context.AuthorizeUsers.ToListAsync();

        foreach (var surveyEvent in surveyEvents)
        {
            surveyEvent.ModifiedByUser = allSafetyUsers.FirstOrDefault(u => u.UserName == surveyEvent.ModifiedBy);
        }

        //foreach (var surveyEvent in surveyEventQuery)
        //{
        //    // we should be able to join to Users table in query above
        //    surveyEvent.ModifiedByUser = this.context.AuthorizeUsers
        //        .FirstOrDefault(su => su.UserName == surveyEvent.ModifiedBy);
        //}

        return surveyEvents;
    }

    public async Task<List<SurveyEvent>> GetSurveyEventsWithResponsesForAuditByFormId(
        Guid formId,
        string formName,
        DateTime earliestDate,
        IQueryable<Models.Common.CommonEncounter> encounters,
        ISafetySurveillanceApplicationContext specificContext)
    {
        var surveyEvents = specificContext.SurveyEvents.TagWith("Audit Survey Event Query")
            .Join(encounters, se => new {se.EncounterSource, se.EncounterId}, e => new {e.EncounterSource, e.EncounterId}, (se, r) => se)
            .Include(se => se.Form)
            .Include(se => se.Responses)
                .ThenInclude(re => re.Question)
            .Include(se => se.ChildSurveyEvents)
                .ThenInclude(ce => ce.Responses)
                    .ThenInclude(re => re.Question)
            .Include(se => se.ChildSurveyEvents)
                .ThenInclude(ce => ce.Form)
                    .ThenInclude(fo => fo.Sections)
                        .ThenInclude(sec => sec.Questions)
            .Include(se => se.StatusReference)
            .Include(se => se.Assignments)
                .ThenInclude(a => a.AssignedToUser)
            .Where(s => s.FormId == formId
                        && s.ParentSurveyEventId == null
                        && !s.IsDeleted
                        && (((s.CreatedDate >= earliestDate)
                             && (!this.formsWithOneFormPerPatient.Contains(s.Form.Name)))
                            || this.formsWithOneFormPerPatient.Contains(FormName.SkinIntegrityAudit)));

        var finalSurveyEvent = await surveyEvents.ToListAsync();

        // ModifiedByUser gets NULLed out by constructor of SurveyEvent during .Include() method
        List<SafetyUser> allSafetyUsers = await specificContext.AuthorizeUsers.ToListAsync();

        foreach (var surveyEvent in finalSurveyEvent)
        {
            surveyEvent.ModifiedByUser = allSafetyUsers.FirstOrDefault(u => u.UserName == surveyEvent.ModifiedBy);
        }

        return finalSurveyEvent;
    }

    public async Task<List<SurveyEvent>> GetSurveyEventsBySurveyDate(Guid formId, DateRange dateRange)
    {
        var surveyEventQuery = this.context.SurveyEvents
            .Where(s => s.FormId == formId && s.ParentSurveyEventId == null && !s.IsDeleted
                        && s.SurveyDate >= dateRange.StartDate && s.SurveyDate <= dateRange.EndDate)
            .Include(s => s.Responses)
            .ThenInclude(response => response.Question)
            .Include(s => s.ChildSurveyEvents)
            .ThenInclude(s => s.Responses)
            .ThenInclude(response => response.Question)
            .Include(s => s.ChildSurveyEvents)
            .ThenInclude(e => e.Form)
            .ThenInclude(f => f.Sections)
            .ThenInclude(s => s.Questions)
            .Include(s => s.StatusReference)
            .Include(s => s.Assignments)
            .ThenInclude(a => a.AssignedToUser);

        return await surveyEventQuery.ToListAsync();
    }

    public async Task<SurveyEvent> GetSurveyEventById(Guid surveyEventId)
    {
        var result = await this.context.SurveyEvents
            .Where(s => !s.IsDeleted && s.SurveyEventId == surveyEventId)
            .Include(c => c.Incident)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(c => c.TriggerEventReview)
            .ThenInclude(c => c.AssociatedServiceLines)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(c => c.TriggerEventReview)
            .ThenInclude(c => c.AssociatedUnits)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(c => c.TriggerEventReview)
            .ThenInclude(c => c.AssociatedDevices)
            .ThenInclude(c => c.Device)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(c => c.TriggerEventReview)
            .ThenInclude(c => c.AssociatedProviders)
            .ThenInclude(c => c.Provider)
            .Include(c => c.ChildSurveyEvents)
            .Include(p => p.Comments)
            .Include(p => p.Responses)
            .ThenInclude(p => p.ResponseOption)
            .Include(p => p.Responses)
            .ThenInclude(p => p.AssociatedPersonResponse)
            .ThenInclude(p => p.Encounter)
            .Include(p => p.Responses)
            .ThenInclude(p => p.AssociatedPersonResponse)
            .ThenInclude(p => p.AssociatedAddress)
            .Include(p => p.Responses)
            .ThenInclude(p => p.DeviceResponses)
            .ThenInclude(p => p.Device)
            .Include(p => p.Responses)
            .ThenInclude(p => p.ProviderResponses)
            .ThenInclude(p => p.Provider)
            .Include(p => p.StatusReference)
            .Include(s => s.CreatedByUser)
            .ToListAsync();

        result = this.triggerMatchService.MatchSurveyEventAssignments(result);
        result = this.triggerMatchService.MatchSurveyTriggerDocumentationStatuses(result);

        var surveyEvent = result.SingleOrDefault(s => s.SurveyEventId == surveyEventId);
        if (surveyEvent.ParentSurveyEventId != null)
        {
            var parentSurveyEvent = await this.context.SurveyEvents
                .Where(s => !s.IsDeleted)
                .Include(children => children.ChildSurveyEvents)
                .ThenInclude(childrenResponses => childrenResponses.Responses)
                .ThenInclude(childrenDeviceResponse => childrenDeviceResponse.DeviceResponses)
                .ThenInclude(childrenDevices => childrenDevices.Device)
                .Include(c => c.ChildSurveyEvents)
                .ThenInclude(c => c.Responses)
                .ThenInclude(c => c.ProviderResponses)
                .ThenInclude(c => c.Provider)
                .Include(s => s.Responses)
                .ThenInclude(r => r.DeviceResponses)
                .ThenInclude(d => d.Device)
                .Include(s => s.CreatedByUser)
                .Include(s => s.ModifiedByUser)
                .FirstOrDefaultAsync(s => s.SurveyEventId == surveyEvent.ParentSurveyEventId);
            surveyEvent.ParentSurveyEvent = parentSurveyEvent;
        }

        if (surveyEvent.ParentSurveyEventId == null)
        {
            var childSurveyEvents = await this.context.SurveyEvents
                .Where(s => !s.IsDeleted && s.ParentSurveyEventId == surveyEvent.SurveyEventId)
                .Include(s => s.Responses)
                .ThenInclude(r => r.DeviceResponses)
                .ThenInclude(d => d.Device)
                .Include(s => s.Responses)
                .ThenInclude(r => r.ProviderResponses)
                .ThenInclude(r => r.Provider)
                .Include(s => s.CreatedByUser)
                .Include(s => s.ModifiedByUser)
                .OrderBy(s => s.Order)
                .ToListAsync();
            surveyEvent.ChildSurveyEvents = childSurveyEvents;
        }

        return surveyEvent;
    }

    public async Task<SurveyEvent> GetParentSurveyEventByEncounter(string encounterId, string encounterSource, Guid formId, string device, string woundId)
    {
        return await this.context.SurveyEvents
            .OrderByDescending(c => c.CreatedDate)
            .FirstOrDefaultAsync(s => s.FormId == formId
                                      && s.EncounterId == encounterId
                                      && s.EncounterSource == encounterSource
                                      && s.ParentSurveyEventId == null
                                      && s.Device == device
                                      && s.WoundId == woundId
                                      && !s.IsDeleted);
    }

    public async Task<SurveyEvent> GetSurveyEventWithParentById(Guid surveyEventId)
    {
        return await this.context.SurveyEvents
            .Where(s => !s.IsDeleted)
            .Include(s => s.ParentSurveyEvent)
            .FirstOrDefaultAsync(s => s.SurveyEventId == surveyEventId);
    }

    public async Task<SurveyEvent> GetSurveyEventWithAssignmentsById(Guid surveyEventId)
    {
        return await this.context.SurveyEvents
            .Where(s => !s.IsDeleted)
            .Include(s => s.Assignments)
            .FirstOrDefaultAsync(s => s.SurveyEventId == surveyEventId);
    }

    public async Task<Guid> CreateSurveyEvent(SurveyEvent surveyEvent, SurveyEvent parentSurvey = null, string facility = null)
    {
        if (parentSurvey != null && parentSurvey.SurveyEventId == Guid.Empty)
        {
            parentSurvey.StatusReference = null;
            parentSurvey.Assignments = null;
            this.context.SurveyEvents.Add(parentSurvey);
        }

        if (surveyEvent.EncounterSource == "IncidentSubmit")
        {
            surveyEvent.Incident = new Models.Incident();
            surveyEvent.Incident.Facility = facility;
        }

        surveyEvent.StatusReference = null;
        surveyEvent.Assignments = null;
        surveyEvent.ModifiedByUser = null;
        surveyEvent.CreatedByUser = null;

        this.context.SurveyEvents.Add(surveyEvent);
        await this.context.SaveChangesAsync();

        return surveyEvent.SurveyEventId;
    }

    public async Task<List<SurveyEventComment>> GetCommentsBySurveyId(Guid surveyEventId)
    {
        return await this.context.SurveyEventComments
            .Include(x => x.SafetyUser)
            .Where(c => c.SurveyEventId == surveyEventId)
            .OrderByDescending(c => c.CommentDate)
            .ToListAsync();
    }

        public async Task<Form> GetFormByIdAsync(Guid formId)
        {
                Form form = await this.context.Forms.Where(f => f.FormId == formId && f.IsActive)
                                .Include(f => f.Sections)
                                    .ThenInclude(s => s.Questions)
                                .Include(f => f.Sections)
                                     .ThenInclude(s => s.Questions)
                                     .ThenInclude(q => q.Options)
                                .Include(f => f.Sections)
                                    .ThenInclude(s => s.Questions)
                                    .ThenInclude(q => q.Options)
                                    .ThenInclude(o => o.SubQuestions)
                                .Include(f => f.Sections)
                                    .ThenInclude(s => s.Questions)
                                    .ThenInclude(q => q.QuestionType)
                                .Include(cf => cf.Sections)
                                    .ThenInclude(cs => cs.Questions)
                                    .ThenInclude(cq => cq.DecisionValues)
                                .Include(cf => cf.Sections)
                                    .ThenInclude(cs => cs.Questions)
                                    .ThenInclude(cq => cq.SubQuestions)
                                    .ThenInclude(sb => sb.DecisionValues)
                                .Include(f => f.Sections)
                                    .ThenInclude(s => s.Questions)
                                    .ThenInclude(q => q.QuestionHelpData)
                                .Include(f => f.ChildForms)
                                    .ThenInclude(cf => cf.Sections)
                                    .ThenInclude(cs => cs.Questions)
                                    .ThenInclude(csq => csq.Options)
                                .Include(f => f.ChildForms)
                                    .ThenInclude(cf => cf.Sections)
                                    .ThenInclude(cs => cs.Questions)
                                    .ThenInclude(csq => csq.Options)
                                    .ThenInclude(o => o.SubQuestions)
                                .Include(f => f.ChildForms)
                                    .ThenInclude(cf => cf.Sections)
                                    .ThenInclude(cs => cs.Questions)
                                    .ThenInclude(cq => cq.QuestionType)
                                .Include(f => f.ChildForms)
                                    .ThenInclude(cf => cf.Sections)
                                    .ThenInclude(cs => cs.Questions)
                                    .ThenInclude(cq => cq.SubQuestions)
                                .Include(f => f.ChildForms)
                                    .ThenInclude(cf => cf.Sections)
                                    .ThenInclude(cs => cs.Questions)
                                    .ThenInclude(cq => cq.SubQuestions)
                                    .ThenInclude(sb => sb.Options)
                                .FirstOrDefaultAsync();

            form.Sections.OrdersSectionQuestionsOptions();
            form.ChildForms.ForEach(c => c.Sections.OrdersSectionQuestionsOptions());

            form.Sections = form.Sections.OrderBy(s => s.Order).ToList();

            form.Sections.RemoveSubQuestions();

            return form;
        }

    public async Task<SurveyStatusReference> GetStatusById(int statusId)
    {
        return await this.context.SurveyStatusReference.FirstOrDefaultAsync(s => s.StatusId == statusId);
    }

    public async Task<Question> GetQuestion(Guid questionId)
    {
        return await this.context.Questions.FirstOrDefaultAsync(q => q.QuestionId == questionId);
    }

    public async Task<Response> GetResponse(Guid responseId)
    {
        return await this.context.Responses
            .Include(r => r.AssociatedPersonResponse)
            .Include(r => r.DeviceResponses)
            .ThenInclude(d => d.Device)
            .Include(r => r.ProviderResponses)
            .ThenInclude(p => p.Provider)
            .Include(r => r.SurveyEvent)
            .Include(r => r.ResponseOption)
            .FirstOrDefaultAsync(r => r.ResponseId == responseId);
    }

    public async Task<Response> GetResponse(Guid surveyEventId, Guid questionId)
    {
        return await this.context.Responses
            .Include(r => r.AssociatedPersonResponse)
            .Include(r => r.DeviceResponses)
            .ThenInclude(d => d.Device)
            .Include(r => r.ProviderResponses)
            .ThenInclude(r => r.Provider)
            .Include(r => r.SurveyEvent)
            .Include(r => r.ResponseOption)
            .FirstOrDefaultAsync(r => r.SurveyEventId == surveyEventId
                                      && r.QuestionId == questionId);
    }

    public async Task SaveSurvey(SurveyEvent surveyEvent)
    {
        this.context.SurveyEvents.Update(surveyEvent);
        if (surveyEvent.ParentSurveyEvent != null)
        {
            this.context.SurveyEvents.Update(surveyEvent.ParentSurveyEvent);
        }

        await this.context.SaveChangesAsync();
    }

    public async Task SaveComment(SurveyEventComment comment, string surveyEncounterSource)
    {
        this.context.SurveyEventComments.Add(comment);
        await this.context.SaveChangesAsync();
    }

    public async Task<List<SurveyEventAssignment>> GetAssignments(Guid surveyEventId)
    {
        return await this.context.SurveyEventAssignments
            .Where(a => a.SurveyEventId == surveyEventId)
            .Include(a => a.AssignedToUser)
            .ToListAsync();
    }

    public async Task<Response> SaveResponse(Response response, SurveyEvent surveyEvent, LocationResponse locationResponse = null)
    {
        if (response.ResponseId != Guid.Empty)
        {
            this.context.Responses.Update(response);
        }
        else
        {
            this.context.Responses.Add(response);
        }

        this.context.SurveyEvents.Update(surveyEvent);
        await this.context.SaveChangesAsync();

        if (locationResponse != null)
        {
            var locationToDelete = await
                this.context.LocationResponses.FirstOrDefaultAsync(l => l.ResponseID == response.ResponseId);

            if (locationToDelete != null)
            {
                this.context.LocationResponses.Remove(locationToDelete);
            }

            locationResponse.ResponseID = response.ResponseId;
            this.context.LocationResponses.Add(locationResponse);
            await this.context.SaveChangesAsync();
        }

        return response;
    }

    public async Task SaveAssignments(
        List<SurveyEventAssignment> assignmentsToSave,
        List<SurveyEventAssignment> assignmentsToDelete)
    {
        foreach (SurveyEventAssignment assignmentToSave in assignmentsToSave)
        {
            assignmentToSave.AssignedToUser = null;
        }

        this.context.SurveyEventAssignments.AddRange(assignmentsToSave);
        this.context.SurveyEventAssignments.RemoveRange(assignmentsToDelete);
        await this.context.SaveChangesAsync();
    }

    public async Task<List<SurveyEvent>> GetSurveyEventsByTypeForPatientEncounterAsync(Guid formId, string encounterId, string encounterSource)
    {
        List<SurveyEvent> surveyEvents = await this.context.SurveyEvents
            .Include(surveyEvent => surveyEvent.Form)
            .ThenInclude(form => form.Sections)
            .ThenInclude(sections => sections.Questions)
            .Include(surveyEvent => surveyEvent.StatusReference)
            .Include(surveyEvent => surveyEvent.Responses)
            .ThenInclude(response => response.Question)
            .Include(surveyEvent => surveyEvent.ChildSurveyEvents)
            .ThenInclude(child => child.Responses)
            .ThenInclude(child => child.Question)
            .Include(surveyEvent => surveyEvent.ChildSurveyEvents)
            .ThenInclude(child => child.Form)
            .ThenInclude(form => form.Sections)
            .ThenInclude(sections => sections.Questions)
            .Include(surveyEvent => surveyEvent.Assignments)
            .ThenInclude(assignment => assignment.AssignedToUser).Where(
                se => se.FormId == formId && se.EncounterId == encounterId
                                          && se.EncounterSource == encounterSource)
            .OrderByDescending(surveyEvent => surveyEvent.CreatedDate)
            .Where(surveyEvent => !surveyEvent.IsDeleted)
            .ToListAsync();

        return surveyEvents;
    }

    public async Task<List<SurveyStatusReference>> GetStatuses()
    {
        return await this.context.SurveyStatusReference.ToListAsync();
    }

    public async Task<List<string>> GetMortalityTypes()
    {
        return await this.context.MortalityAudits.Select(x => x.DeathTypeDsc).Distinct().ToListAsync();
    }

    public async Task<List<string>> GetFilterUnits()
    {
        return await this.context.CommonAdts.Select(s => s.Unit).Distinct().ToListAsync();
    }

    public async Task<List<SurveyEvent>> GetSurveyEventsForPatientEncounterAsync(string encounterId, string encounterSource)
    {
        List<SurveyEvent> surveyEvents = await this.context.SurveyEvents
            .Include(surveyEvent => surveyEvent.StatusReference)
            .Include(surveyEvent => surveyEvent.Responses)
            .ThenInclude(response => response.Question)
            .Include(surveyEvent => surveyEvent.ChildSurveyEvents)
            .ThenInclude(child => child.Responses)
            .ThenInclude(response => response.Question)
            .Include(surveyEvent => surveyEvent.ChildSurveyEvents)
            .Include(surveyEvent => surveyEvent.Assignments)
            .ThenInclude(assignment => assignment.AssignedToUser).Where(
                se => se.EncounterId == encounterId
                      && se.EncounterSource == encounterSource)
            .OrderByDescending(surveyEvent => surveyEvent.CreatedDate)
            .Where(surveyEvent => !surveyEvent.IsDeleted && surveyEvent.Form.ParentFormId == null)
            .ToListAsync();

        return surveyEvents;
    }

    private async Task<IQueryable<SurveyEvent>> GetAllSurveyEventBaseQueryable()
    {
        var enabledAuditToolIds = await this.configurationRepository.GetEnabledAuditTools();
        var auditToolReferences = await this.configurationRepository.GetAuditToolsAsync();

        var enabledAuditTools = auditToolReferences.Where(c => enabledAuditToolIds.ToList().Contains(c.AuditToolId.ToString()));

        IQueryable<SurveyEvent> resultQuery = this.context.SurveyEvents
            .Include(c => c.Incident)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(c => c.TriggerEventReview)
            .ThenInclude(d => d.AdverseEventCategoryReference)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(c => c.TriggerEventReview)
            .ThenInclude(d => d.AdverseEventSubCategoryReference)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(d => d.TriggerEventReview)
            .ThenInclude(d => d.AssociatedUnits)
            .Include(c => c.TriggerEventToReview)
            .ThenInclude(d => d.TriggerEventReview)
            .ThenInclude(d => d.AssociatedServiceLines)
            .Include(d => d.TriggerEventToReview)
            .ThenInclude(x => x.TriggerEventComments)
            .Include(p => p.Comments)
            .Include(p => p.Responses)
            .ThenInclude(p => p.ResponseOption)
            .Include(p => p.Responses)
            .ThenInclude(p => p.DeviceResponses)
            .ThenInclude(p => p.Device)
            .Include(p => p.Responses)
            .ThenInclude(p => p.ProviderResponses)
            .ThenInclude(p => p.Provider)
            .Include(p => p.Responses)
            .ThenInclude(p => p.AssociatedPersonResponse)
            .ThenInclude(p => p.Person)
            .Include(p => p.Responses)
            .ThenInclude(p => p.AssociatedPersonResponse)
            .ThenInclude(p => p.Encounter)
            .Include(p => p.Responses)
            .Include(se => se.HistoricalEncounter)
            .Include(se => se.Encounter)
            .Include(s => s.CreatedByUser)
            .Include(s => s.ModifiedByUser);

        if(enabledAuditTools.Any(c => c.AuditToolName == "mortality"))
        {
            resultQuery = resultQuery
                .Include(p => p.Encounter)
                    .ThenInclude(p => p.MortalityAudit);
        }

        if(enabledAuditTools.Any(c => c.AuditToolName == "readmission"))
        {
            resultQuery = resultQuery
                .Include(p => p.Encounter)
                    .ThenInclude(p => p.ReadmissionAudit);
        }

        return resultQuery;
    }

    public async Task<List<SurveyEvent>> GetAllSurveyEvents(DateRange dateRange, List<Guid> formIds, List<string> facilities, bool filterByDataDts, bool isIncident)
    {
        IQueryable<SurveyEvent> resultQuery = await GetAllSurveyEventBaseQueryable();
        List<SurveyEvent> result;

        if (filterByDataDts)
        {
            result = await resultQuery
                .Where(p => p.DataDts >= dateRange.StartDate
                            && p.DataDts <= dateRange.EndDate
                            && !p.IsDeleted
                            && formIds.Contains(p.FormId)
                ).ToListAsync();

            result = result.Where(s => facilities.Contains(s.Facility)).ToList();
        }
        else
        {
            if (isIncident)
            {
                result = await resultQuery
                    .Include(rq => rq.Incident)
                    .Include(se => se.TriggerEventToReview).ThenInclude(tetr => tetr.TriggerEventReview)
                    .ThenInclude(ter => ter.ScaleItem.ScaleType)
                    .Where(p => p.Incident.SubmittedDts >= dateRange.StartDate && p.Incident.SubmittedDts <= dateRange.EndDate).ToListAsync();
            }
            else
            {
                result = await resultQuery
                    .Where(p =>
                        p.SurveyDate >= dateRange.StartDate &&
                        p.SurveyDate <= dateRange.EndDate &&
                        !p.IsDeleted &&
                        formIds.Contains(p.FormId))
                    .ToListAsync();
            }
        }

        result = result.Select(x => x.SetEncounter()).ToList();

        result = result
            .Where(p => (
                facilities.Any(q => p.Encounter != null && p.Encounter.Location == q)
                || facilities.Any(q =>
                    p.Responses.Any(d =>
                        d.AssociatedPersonResponse != null && d.AssociatedPersonResponse.Location == q))))
            .ToList();

        foreach(var surveyEvent in result)
        {

            var anonymousResp = surveyEvent.Responses
                .FirstOrDefault(c => c.QuestionId == SurveyQuestionsByIdConstants.AnonymousSurveyQuestionId)
                ?.ResponseText;
            if (anonymousResp != "No")
            {
                surveyEvent.CreatedBy = "(submitted anonymously)";
                surveyEvent.CreatedByUser = new SafetyUser();
            }

            if (surveyEvent.CreatedBy != "(submitted anonymously)")
            {
                surveyEvent.CreatedByFullNameAnonymized = surveyEvent.CreatedByUser.DisplayName;
            }
            else
            {
                surveyEvent.CreatedByFullNameAnonymized = "Anonymous";
            }

            if (surveyEvent.AssociatedPerson != null)
            {
                surveyEvent.AssociatedPerson.Gender = surveyEvent.AssociatedPerson.Encounter?.Gender ?? surveyEvent.AssociatedPerson.Gender;
                surveyEvent.AssociatedPerson.Race = surveyEvent.AssociatedPerson.Encounter?.RaceDsc ?? surveyEvent.AssociatedPerson.Race;
                surveyEvent.AssociatedPerson.Ethnicity = surveyEvent.AssociatedPerson.Encounter?.EthnicGroupDSC ?? surveyEvent.AssociatedPerson.Ethnicity;
            }

            if (surveyEvent.TriggerEventToReview != null && surveyEvent.TriggerEventToReview.TriggerEventReview != null)
            {
                surveyEvent.TriggerEventToReview.TriggerEventReview.TriggerEventInvestigationDetails = await this
                    .triggerEventInvestigationDetailRepository.GetTriggerEventInvestigationDetailsAsync(
                        surveyEvent.TriggerEventToReview.TriggerId,
                        surveyEvent.TriggerEventToReview.PatientEncounterId,
                        surveyEvent.TriggerEventToReview.TriggerSourceDataId);
            }
        }

        return result;
    }

    public async Task<List<Question>> GetAllQuestionsForForm(string formName)
    {
        var auditToolName = formName;

        switch (formName)
        {
            case "Pressure Injury Assessment":
                auditToolName = "Pressure Injury Audit";
                break;
            case "30 Day Readmission":
                auditToolName = "30-Day Readmission Audit";
                break;
        }

        // Get active forms
        var enabledAudits = await configurationRepository.GetEnabledAuditTools();

        var audit = await this.context.AuditToolReferences
            .Where(a => a.AuditToolDescription == auditToolName && enabledAudits.Contains(a.AuditToolId.ToString()))
            .FirstOrDefaultAsync();

        if (audit == null) return new List<Question>();

        var auditVersion = audit.Version ?? 1;

        List<Question> parentQuestions = await (
            from forms in this.context.Forms
            join section in context.Sections on forms.FormId equals section.FormId
            join question in context.Questions on section.SectionId equals question.SectionId
            where (forms.Name == formName || forms.Name == auditToolName) && forms.Version == auditVersion
            orderby section.Order, question.Order
            select question).ToListAsync();

        // might have to tweak this to be recursive more than one layer deep.  Recursion may not be fully possible
        // because of this being entity framework calls.  I dont know man
        List<Question> childQuestions = await (
            from parentForm in this.context.Forms
            join childForm in this.context.Forms on parentForm.FormId equals childForm.ParentFormId
            join section in context.Sections on childForm.FormId equals section.FormId
            join question in context.Questions on section.SectionId equals question.SectionId
            where parentForm.Name == formName && parentForm.Version == auditVersion
            orderby question.Order
            select question
        ).ToListAsync();

        return parentQuestions.Concat(childQuestions)
            .ToList();
    }

    public async Task ResetIncident(Guid surveyEventId)
    {
        var responses = await context.Responses.Where(x => x.SurveyEventId == surveyEventId && !x.IsDeleted).ToListAsync();
        var associatedPersons = await context.AssociatedPersons.Include(x => x.AssociatedAddress).Where(x => responses.Select(y => y.ResponseId).Contains(x.ResponseId)).ToListAsync();
        var associatedLocations = await context.LocationResponses.Where(x => responses.Select(y => y.ResponseId).Contains(x.ResponseID)).ToListAsync();

        context.LocationResponses.RemoveRange(associatedLocations);
        context.AssociatedPersons.RemoveRange(associatedPersons);
        context.Responses.RemoveRange(responses);
        await context.SaveChangesAsync();
    }

    public async Task UpdateIncidentFacility(Guid surveyEventId, string facility)
    {
        var surveyEvent = await this.context.SurveyEvents
            .Include(se => se.Incident)
            .FirstOrDefaultAsync(se => se.SurveyEventId == surveyEventId);

        await UpdateIncidentFacility(surveyEvent, facility);
    }

    private async Task UpdateIncidentFacility(SurveyEvent surveyEvent, string facility)
    {
        if (surveyEvent?.Incident is null)
        {
            return;
        }

        surveyEvent.Incident.Facility = facility;
        await this.context.SaveChangesAsync();
    }
}
