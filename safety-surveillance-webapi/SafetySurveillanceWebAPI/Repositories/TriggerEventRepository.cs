using EFCoreSecondLevelCacheInterceptor;
using SafetySurveillanceWebApi.Controllers;
using SafetySurveillanceWebApi.Models.Incidents;
using SafetySurveillanceWebApi.Services.Authorization.AccessControl;

namespace SafetySurveillanceWebApi.Repositories
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.EntityFrameworkCore;
    using SafetySurveillanceWebApi.Data;
    using SafetySurveillanceWebApi.Extensions;
    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Models.Common;
    using SafetySurveillanceWebApi.Models.Constants;
    using SafetySurveillanceWebApi.Models.Dtos.AdverseEventFilters;
    using SafetySurveillanceWebApi.Models.PublicHealth;
    using SafetySurveillanceWebApi.Models.Survey;
    using SafetySurveillanceWebApi.Services;

    public class TriggerEventRepository : ITriggerEventRepository
    {
        private readonly ISafetySurveillanceApplicationContext safetySurveillanceApplicationContext;

        private readonly ITriggerReviewRepository triggerReviewRepository;
        private readonly ITriggerEventCommentRepository commentRepository;
        private readonly IAdverseEventFilterService adverseEventFilterService;

        private readonly IApplicationConfigurationRepository appConfigRepository;
        private readonly ITriggerMatchService triggerMatchService;
        private readonly ITriggerSearchService triggerSearchService;

        public TriggerEventRepository(
            ISafetySurveillanceApplicationContext safetySurveillanceApplicationContext,
            ITriggerReviewRepository triggerReviewRepository,
            IAdverseEventFilterService adverseEventFilterService,
            IApplicationConfigurationRepository appConfigRepository,
            ITriggerMatchService triggerMatchService,
            ITriggerSearchService triggerSearchService,
            ITriggerEventCommentRepository commentRepository)
        {
            this.triggerReviewRepository = triggerReviewRepository;
            this.adverseEventFilterService = adverseEventFilterService;
            this.appConfigRepository = appConfigRepository;
            this.safetySurveillanceApplicationContext = safetySurveillanceApplicationContext;
            this.triggerMatchService = triggerMatchService;
            this.triggerSearchService = triggerSearchService;
            this.commentRepository = commentRepository;
        }

        public async Task<List<string>> GetFacilitiesAsync()
        {
            return await this.safetySurveillanceApplicationContext.CommonEncounters
                .Select(encounter => encounter.Location)
                .Where(facility => facility != null && facility != "NULL")
                .Distinct()
                .OrderBy(n => n)
                .ToListAsync();
        }

        public async Task<object> GetTriggerCategoriesAndReferencesAsync(string surveillanceType)
        {
            List<string> categoriesList = new List<string>();

            var triggerIdsToDisplay = await this.appConfigRepository.GetSelectedTriggerIds();

            List<string> triggerIds = await this.safetySurveillanceApplicationContext.TriggerEvents
                .Select(trigger => trigger.TriggerId)
                .Where(
                    triggerId => triggerIdsToDisplay.Contains(triggerId) && triggerId != null)
                .Distinct()
                .ToListAsync();

            var allTriggerReferences = await this.safetySurveillanceApplicationContext.TriggerReferences
                .Where(triggerReference => triggerReference.TriggerCategory != null
                                           && triggerReference.SurveillanceType == surveillanceType
                                           && triggerIdsToDisplay.Contains(triggerReference.TriggerId))
                .ToListAsync();

            return allTriggerReferences.GroupBy(c => c.TriggerCategory).Select(c => new
            {
                Category = c.Key, Triggers = c.Select(d => new {Name = d.TriggerName, Value = d.TriggerId}).ToList()
            });
        }

        public async Task<List<string>> GetTriggerCategoriesAsync(string surveillanceType)
        {
            List<string> categoriesList = new List<string>();

            var triggerIdsToDisplay = await this.appConfigRepository.GetSelectedTriggerIds();

            List<string> triggerIds = await this.safetySurveillanceApplicationContext.TriggerEvents
                .Select(trigger => trigger.TriggerId)
                .Where(
                    triggerId => triggerIdsToDisplay.Contains(triggerId) && triggerId != null)
                .Distinct()
                .ToListAsync();

            var allTriggerReferences = await this.safetySurveillanceApplicationContext.TriggerReferences
                .Where(triggerReference => triggerReference.TriggerCategory != null &&
                                           triggerReference.SurveillanceType == surveillanceType)
                .ToListAsync();

            foreach (string triggerId in triggerIds)
            {
                string category = allTriggerReferences
                    .FirstOrDefault(triggerReference => triggerReference.TriggerId == triggerId)
                    ?.TriggerCategory;

                if (!categoriesList.Contains(category) && category != null)
                {
                    categoriesList.Add(category);
                }
            }

            categoriesList.Sort();

            return categoriesList;
        }

        public async Task<TriggerDetail> GetTriggerEventAsync(string triggerId, string patientEncounterId,
            string sourceDataId, UserName userName, DateTime? triggerDts = null)
        {
            Guard.Against.NullOrEmpty(triggerId, nameof(triggerId));
            Guard.Against.NullOrEmpty(patientEncounterId, nameof(patientEncounterId));
            Guard.Against.NullOrEmpty(sourceDataId, nameof(sourceDataId));

            var triggerEvent = new TriggerDetail();

            if (triggerId != "9999")
            {
                var historicalTriggerDetails = (await this.safetySurveillanceApplicationContext.HistoricalTriggerDetails
                    .Include(tr => tr.Encounter)
                    .Include(tr => tr.TriggerReference)
                    .Where(tr =>
                        tr.PatientEncounterId == patientEncounterId && tr.TriggerId == triggerId &&
                        tr.TriggerSourceDataId == sourceDataId).ToListAsync());

                var triggerDetails = (await this.safetySurveillanceApplicationContext.TriggerEvents
                    .Include(tr => tr.Encounter)
                    .Include(tr => tr.TriggerReference)
                    .Where(tr =>
                        tr.PatientEncounterId == patientEncounterId && tr.TriggerId == triggerId &&
                        tr.TriggerSourceDataId == sourceDataId).ToListAsync());

                triggerEvent = triggerDetails.FirstOrDefault() ??
                               historicalTriggerDetails.ToTriggerDetailList().FirstOrDefault();
            }
            else
            {
                triggerEvent.TriggerId = triggerId;
                triggerEvent.PatientEncounterId = patientEncounterId;
                triggerEvent.TriggerSourceDataId = sourceDataId;
                triggerEvent.TriggerSourceDataTypeDsc = "IncidentSubmit";
                triggerEvent.TriggerDts = triggerDts ?? new DateTime();
            }

            var triggerEventReview = await this.triggerReviewRepository.GetTriggerEventReviewAsync(
                triggerEvent.TriggerId,
                triggerEvent.PatientEncounterId,
                triggerEvent.TriggerSourceDataId);

            var triggerEventStatus = await this.triggerReviewRepository.GetTriggerStatusAsync(
                triggerEvent.TriggerId,
                triggerEvent.PatientEncounterId,
                triggerEvent.TriggerSourceDataId);

            var triggerEventAssignment = await this.triggerReviewRepository.GetTriggerEventAssignmentAsync(
                triggerEvent.TriggerId,
                triggerEvent.PatientEncounterId,
                triggerEvent.TriggerSourceDataId);

            triggerEvent.TriggerEventReview = triggerEventReview;
            triggerEvent.TriggerStatus = triggerEventStatus;
            triggerEvent.TriggerEventAssignment = triggerEventAssignment;

            return triggerEvent;
        }

        public IEnumerable<TriggerDetail> GetTriggerEventsForPatientEncounter(string patientEncounterId,
            string surveillanceType, UserName userName)
        {
            Guard.Against.NullOrEmpty(patientEncounterId, nameof(patientEncounterId));

            var triggerIdsToDisplay = this.appConfigRepository.GetSelectedTriggerIds().Result;

            List<TriggerDetail> triggerEvents = this.safetySurveillanceApplicationContext.TriggerEvents
                .Include(c => c.Encounter)
                .Include(t => t.TriggerReference)
                .Include(tf => tf.TriggerFlags)
                .Where(
                    triggerEvent => triggerEvent.PatientEncounterId == patientEncounterId
                                    && triggerIdsToDisplay.Contains(triggerEvent.TriggerId) &&
                                    triggerEvent.TriggerReference.SurveillanceType == surveillanceType)
                .OrderByDescending(triggerEvent => triggerEvent.TriggerDts).ToList();

            triggerEvents = this.triggerMatchService
                .MatchTriggerReviewsForPatientEncounter(triggerEvents, patientEncounterId).Result;

            triggerEvents = this.triggerMatchService.MatchPatientWatches(triggerEvents, userName);

            triggerEvents = this.triggerMatchService.MatchTriggerStatuses(triggerEvents);

            triggerEvents =
                this.triggerMatchService.MatchPublicHealthEventReviewForPatientEncounter(triggerEvents,
                    patientEncounterId).Result;

            triggerEvents = this.triggerMatchService.MatchTriggerEventAssignments(triggerEvents);

            return triggerEvents;
        }

        private async Task<List<TriggerDetail>> GetListOfTriggerDetailsByAdverseEventDate(
            string[] triggerIdsToDisplay,
            List<string> facilities,
            string surveillanceType,
            DateRange dateRange)
        {
            // Get all the links between trigger events and reviews based on adverse event dates
            IQueryable<TriggerEventToReview> triggerEventToReviews = this.safetySurveillanceApplicationContext
                .TriggerEventToReviews
                .Include(tetr => tetr.TriggerEventReview)
                .Where(ter =>
                    ter.TriggerEventReview.AdverseEventDts >= dateRange.StartDate &&
                    ter.TriggerEventReview.AdverseEventDts <= dateRange.EndDate);

            IQueryable<HistoricalTriggerDetail> triggerEventsAeQueryable = this.safetySurveillanceApplicationContext
                .HistoricalTriggerDetails
                .Include(c => c.Encounter)
                .Include(t => t.TriggerReference)
                .Include(tf => tf.TriggerFlags)
                .Where(triggerEvent => triggerIdsToDisplay.Contains(triggerEvent.TriggerId) &&
                                       facilities.Contains(triggerEvent.Encounter.Location) &&
                                       triggerEvent.TriggerReference.SurveillanceType == surveillanceType);

            // Joining the two lists based on the adverse event dates and returning the historical trigger details
            IQueryable<HistoricalTriggerDetail> triggerEventsQueryable =
                from tetr in triggerEventToReviews
                join teaq in triggerEventsAeQueryable on
                    new {tetr.TriggerId, tetr.PatientEncounterId, tetr.TriggerSourceDataId} equals
                    new {teaq.TriggerId, teaq.PatientEncounterId, teaq.TriggerSourceDataId}
                where tetr.TriggerEventReview.AdverseEventDts >= dateRange.StartDate &&
                      tetr.TriggerEventReview.AdverseEventDts <= dateRange.EndDate
                select teaq;

            return (await triggerEventsAeQueryable.ToListAsync()).ToTriggerDetailList();
        }

        private async Task<List<TriggerDetail>> GetListOfIncidentTriggerDetailsByAdverseEventDate(
            string[] triggerIdsToDisplay,
            List<string> facilities,
            string surveillanceType,
            DateRange dateRange)
        {
            // Get all the links between trigger events and reviews based on adverse event dates
            IQueryable<TriggerEventToReview> triggerEventToReviews = this.safetySurveillanceApplicationContext
                .TriggerEventToReviews
                .Include(tetr => tetr.TriggerEventReview)
                .Where(ter =>
                    ter.TriggerEventReview.AdverseEventDts >= dateRange.StartDate &&
                    ter.TriggerEventReview.AdverseEventDts <= dateRange.EndDate);

            var incidentsForManuallyEnteredPerson = from a in safetySurveillanceApplicationContext.AssociatedPersons
                join p in safetySurveillanceApplicationContext.PersonTypeReferences on a.PersonTypeId equals
                    p.Id into ap
                from p in ap.DefaultIfEmpty()
                join r in safetySurveillanceApplicationContext.Responses on a.ResponseId equals r.ResponseId
                    into pr
                from r in pr.DefaultIfEmpty()
                join s in safetySurveillanceApplicationContext.SurveyEvents on r.SurveyEventId equals s
                        .SurveyEventId
                    into rs
                from s in rs.DefaultIfEmpty()
                join td in safetySurveillanceApplicationContext.HistoricalTriggerDetails.Include(c =>
                        c.TriggerReference) on s.SurveyEventId.ToString() equals
                    td.TriggerSourceDataId
                where a.IsManual.Equals(true) && td.TriggerId.Equals("9999")
                                              && facilities.Contains(a.Location)
                                              && triggerIdsToDisplay.Contains(td.TriggerId)
                                              && td.TriggerReference.SurveillanceType.Equals(surveillanceType)
                select new {TriggerDetail = td, AssociatedPerson = a, Person = p};

            // Joining the two lists based on the adverse event dates and returning the historical trigger details
            var triggerEventsQueryable =
                from tetr in triggerEventToReviews
                join ifmep in incidentsForManuallyEnteredPerson on
                    new {tetr.TriggerId, tetr.PatientEncounterId, tetr.TriggerSourceDataId} equals
                    new
                    {
                        ifmep.TriggerDetail.TriggerId,
                        ifmep.TriggerDetail.PatientEncounterId,
                        ifmep.TriggerDetail.TriggerSourceDataId
                    }
                where tetr.TriggerEventReview.AdverseEventDts >= dateRange.StartDate &&
                      tetr.TriggerEventReview.AdverseEventDts <= dateRange.EndDate
                select ifmep;

            var adverseEventDateFilteredIncidents = await triggerEventsQueryable.ToListAsync();

            return adverseEventDateFilteredIncidents.Select(a =>
            {
                a.TriggerDetail.AssociatedPersonLocation = a.AssociatedPerson.Location;
                a.TriggerDetail.AssociatedPerson = a.AssociatedPerson;
                a.TriggerDetail.AssociatedPerson.Person = a.Person;
                if (a.TriggerDetail.Encounter == null)
                {
                    a.TriggerDetail.AssociatedPerson.IsManual = true;
                    a.TriggerDetail.Encounter = new HistoricalCommonEncounter()
                    {
                        FirstName = a.AssociatedPerson.FirstName,
                        LastName = a.AssociatedPerson.LastName,
                        FacilityAccountId = a.AssociatedPerson.VisitNumber,
                        Mrn = a.AssociatedPerson.ResponseId.ToString(),
                        Facility = a.AssociatedPerson.Location
                    };
                }
                else
                {
                    a.TriggerDetail.AssociatedPerson = new AssociatedPerson()
                    {
                        FirstName = a.TriggerDetail.Encounter.FirstName,
                        LastName = a.TriggerDetail.Encounter.LastName,
                        VisitNumber = a.TriggerDetail.Encounter.FacilityAccountId,
                        Mrn = a.TriggerDetail.Encounter.Mrn,
                        IsManual = false,
                        EncounterId = a.TriggerDetail.Encounter.EncounterId,
                        EncounterSource = a.TriggerDetail.Encounter.EncounterSource,
                        PersonTypeId = a.AssociatedPerson.PersonTypeId,
                        Person = a.AssociatedPerson.Person
                    };
                }

                return a.TriggerDetail;
            }).ToList().ToTriggerDetailList();
        }

        public async Task<List<TriggerDetail>> GetTriggerEventsWithReviewsAsync(
            List<string> facilities,
            DateRange dateRange,
            string surveillanceType,
            AdverseEventFilterData filterData,
            List<string> selectedTrigger,
            string userName,
            bool positiveReviewsOnly = false,
            bool filterByAeDate = false,
            bool uniqueReview = false,
            bool aeDtsInDates = false,
            IAccessControlStrategy slAccessControlStrategy = null)
        {
            Guard.Against.Null(facilities, nameof(facilities));


            string[] triggerIdsToDisplay;

            if (selectedTrigger == null)
            {
                triggerIdsToDisplay = await this.appConfigRepository.GetSelectedTriggerIds();
            }
            else
            {
                triggerIdsToDisplay = selectedTrigger.ToArray();
            }

            List<TriggerDetail> triggerEvents = new List<TriggerDetail>();

            if (filterByAeDate)
            {
                triggerEvents = await GetListOfTriggerDetailsByAdverseEventDate(triggerIdsToDisplay, facilities, surveillanceType, dateRange);

                var incidents = await GetListOfIncidentTriggerDetailsByAdverseEventDate(triggerIdsToDisplay, facilities, surveillanceType, dateRange);

                triggerEvents = triggerEvents.Select(c =>
                {
                    if (c.Encounter != null)
                    {
                        c.AssociatedPerson = new AssociatedPerson()
                        {
                            FirstName = c.Encounter.FirstName,
                            LastName = c.Encounter.LastName,
                            VisitNumber = c.Encounter.FacilityAccountId,
                            Mrn = c.Encounter.Mrn,
                            IsManual = false,
                            EncounterId = c.Encounter.EncounterId,
                            EncounterSource = c.Encounter.EncounterSource,
                            PersonTypeId = 1,
                            Person = new PersonTypeReference() {Id = 1, PersonType = "Patient", IsActive = true}
                        };
                    }

                    return c;
                }).ToList();

                triggerEvents.AddRange(incidents);
            }
            else
            {
                IQueryable<TriggerDetail> triggerEventsQueryable = this.safetySurveillanceApplicationContext
                    .TriggerEvents
                    .Include(c => c.Encounter)
                    .Include(t => t.TriggerReference)
                    .Include(tf => tf.TriggerFlags)
                    .Where(triggerEvent => triggerIdsToDisplay.Contains(triggerEvent.TriggerId)
                                           && facilities.Contains(triggerEvent.Encounter.Location)
                                           && triggerEvent.TriggerReference.SurveillanceType == surveillanceType
                                           && triggerEvent.TriggerDts >= dateRange.StartDate &&
                                           triggerEvent.TriggerDts <= dateRange.EndDate);

                triggerEvents = await triggerEventsQueryable.ToListAsync();
            }

            triggerEvents = await this.triggerMatchService.MatchTriggerReviews(triggerEvents, dateRange, surveillanceType, positiveReviewsOnly, filterByAeDate);

            triggerEvents = this.triggerMatchService.MatchPatientWatches(triggerEvents, userName);

            triggerEvents = this.triggerMatchService.MatchTriggerStatuses(triggerEvents);

            triggerEvents = this.triggerMatchService.MatchTriggerEventAssignments(triggerEvents);

            triggerEvents = this.triggerSearchService.SearchTriggerEvents(triggerEvents, filterData);

            triggerEvents =
                await this.adverseEventFilterService.FilterTriggerResults(triggerEvents, filterData, userName,
                    slAccessControlStrategy);

            triggerEvents = await this.commentRepository.MatchTriggerCommentsAsync(triggerEvents);

            if (uniqueReview && surveillanceType == SurveillanceTypeConstants.Harm)
            {
                triggerEvents = this.triggerMatchService.ShowUniqueReviewsOnly(triggerEvents);
            }

            return triggerEvents;
        }

        public List<TriggerDetail> GetFilteredTriggers(List<string> facilities, string userId, bool assignedFilter,
            bool watchedFilter, string surveillanceType)
        {
            List<TriggerDetail> triggerEvents = new List<TriggerDetail>();

            if (assignedFilter)
            {
                triggerEvents = this.triggerMatchService.MatchTriggerAssignee(userId, surveillanceType)
                    .Result;
            }

            if (watchedFilter)
            {
                var watchCheck = this.triggerMatchService.MatchPatientWatch(facilities, userId, surveillanceType)
                    .Result;
                foreach (var triggerDetail in watchCheck)
                {
                    if (!triggerEvents.Contains(triggerDetail))
                    {
                        triggerEvents.Add(triggerDetail);
                    }
                }
            }

            triggerEvents = this.triggerMatchService.MatchTriggerStatuses(triggerEvents);

            triggerEvents = this.triggerMatchService.MatchPatientWatches(triggerEvents, userId);

            triggerEvents = this.triggerMatchService.MatchTriggerEventAssignments(triggerEvents);

            return triggerEvents;
        }

        public async Task<List<TriggerDetail>> GetRelatedTriggerEventsAsync(TriggerDetail triggerDetail)
        {
            DateTime startDate = triggerDetail.TriggerDts.AddDays(-1);
            DateTime endDate = triggerDetail.TriggerDts.AddDays(1);

            var trigger = await this.safetySurveillanceApplicationContext.TriggerReferences
                .Where(t => t.TriggerId == triggerDetail.TriggerId).Select(t => t).FirstOrDefaultAsync();

            var triggerEvents = await this.safetySurveillanceApplicationContext.TriggerEvents.Where(
                    triggerEvent => triggerEvent.TriggerId == triggerDetail.TriggerId
                                    && triggerEvent.PatientEncounterId == triggerDetail.PatientEncounterId
                                    && triggerEvent.TriggerDts >= startDate && triggerEvent.TriggerDts <= endDate
                                    && triggerEvent.TriggerSourceDataId != triggerDetail.TriggerSourceDataId)
                .Include(t => t.TriggerReference)
                .Include(t => t.Encounter)
                .OrderBy(o => o.TriggerDts)
                .ToListAsync();

            triggerEvents = await this.triggerMatchService.MatchTriggerReviews(triggerEvents, new DateRange(startDate, endDate), trigger.SurveillanceType.ToLower());

            if (triggerDetail.PatientEncounterId == null)
            {
                return triggerEvents;
            }

            {
                var currentLink = await this.triggerReviewRepository.GetTriggerEventToReviewForTriggerDetail(
                    triggerDetail.TriggerId,
                    triggerDetail.PatientEncounterId,
                    triggerDetail.TriggerSourceDataId);


                triggerEvents = triggerEvents.Where(
                    triggerEvent => triggerEvent.TriggerEventReview == null
                                    || (currentLink != null && triggerEvent.TriggerEventReview.ReviewId
                                        == currentLink.TriggerReviewId)).ToList();
            }

            return triggerEvents;
        }

        public async Task<List<TriggerEventToReview>> GetTriggerEventsToReviewAsync(int reviewId)
        {
            var returnable = await this.safetySurveillanceApplicationContext.TriggerEventToReviews
                .Where(trigger => trigger.TriggerReviewId == reviewId).OrderBy(o => o.TriggerSourceDataId)
                .ToListAsync() ?? new List<TriggerEventToReview>();
            return returnable;
        }

        public async Task<List<TriggerReference>> GetTriggerReferencesAsync(string category)
        {
            var triggerIdsToDisplay = await this.appConfigRepository.GetSelectedTriggerIds();

            List<TriggerReference> triggerReferences = await this.safetySurveillanceApplicationContext
                .TriggerReferences
                .Where(reference => reference.TriggerCategory == category
                                    && triggerIdsToDisplay.Contains(reference.TriggerId))
                .Cacheable()
                .ToListAsync();

            return triggerReferences;
        }

        public async Task<List<TriggerReference>> GetTriggerReferencesBySurveillanceTypeAsync(string surveillanceType)
        {
            var triggerIdsToDisplay = await this.appConfigRepository.GetSelectedTriggerIds();

            List<TriggerReference> triggerReferences = await this.safetySurveillanceApplicationContext.TriggerReferences
                .Where(reference => reference.SurveillanceType == surveillanceType
                                    && triggerIdsToDisplay.Contains(reference.TriggerId)).ToListAsync();

            return triggerReferences;
        }

        public async Task<List<TriggerReference>> GetTriggerReferencesAsync()
        {
            return await this.safetySurveillanceApplicationContext.TriggerReferences
                .Where(t => t.TriggerCategory != null && t.TriggerCategory != "NULL").ToListAsync();
        }

        public List<TriggerDetail> GetUniqueAdverseEventReviewByMrn(List<TriggerDetail> triggers)
        {
            return triggers.GroupBy(x => new {x.Encounter}).Select(o => o.FirstOrDefault()).ToList();
        }

        public async Task<TriggerDetail> GetTriggerByTriggerId(string id, string encounterId, DateTime eventDts,
            string triggerSourceDataId)
        {
            Dictionary<string, string> selectedTriggerIds = (await this.appConfigRepository.GetSelectedTriggerIds())
                .ToDictionary(triggerValue => triggerValue);
            TriggerDetail trigger = this.safetySurveillanceApplicationContext.TriggerEvents
                .Where(t => t.TriggerId == id
                            && t.PatientEncounterId == encounterId
                            && t.TriggerDts == eventDts
                            && t.TriggerSourceDataId == triggerSourceDataId)
                .Include(tr => tr.TriggerReference)
                .Include(tr => tr.Encounter)
                .FirstOrDefault();

            if (trigger?.TriggerReference != null)
            {
                trigger.TriggerReference.Selected = selectedTriggerIds.ContainsKey(trigger.TriggerId);
            }

            return trigger;
        }

        public async Task<List<TriggerDetail>> GetRelatedPublicHealthTriggerEventsAsync(TriggerDetail triggerDetail)
        {
            DateTime startDate = triggerDetail.TriggerDts.AddDays(-1);
            DateTime endDate = triggerDetail.TriggerDts.AddDays(1);

            var triggerEvents = await this.safetySurveillanceApplicationContext.TriggerEvents.Where(
                    triggerEvent => triggerEvent.TriggerId == triggerDetail.TriggerId
                                    && triggerEvent.PatientEncounterId == triggerDetail.PatientEncounterId
                                    && triggerEvent.TriggerDts >= startDate && triggerEvent.TriggerDts <= endDate
                                    && triggerEvent.TriggerSourceDataId != triggerDetail.TriggerSourceDataId)
                .Include(t => t.TriggerReference)
                .Include(t => t.Encounter)
                .OrderBy(o => o.TriggerDts)
                .ToListAsync();

            triggerEvents = await this.triggerMatchService.MatchTriggerReviews(triggerEvents, new DateRange(startDate, endDate), SurveillanceTypeEnum.PublicHealth.ToStringEnum());

            var currentLink = await this.triggerReviewRepository.GetTriggerEventToReviewForTriggerDetail(
                triggerDetail.TriggerId,
                triggerDetail.PatientEncounterId,
                triggerDetail.TriggerSourceDataId);

            triggerEvents = triggerEvents.Where(
                triggerEvent => triggerEvent.TriggerEventReview == null
                                || (currentLink != null && triggerEvent.TriggerEventReview.ReviewId
                                    == currentLink.TriggerReviewId)).ToList();

            return triggerEvents;
        }

        public async Task<List<PublicHealthToReview>> GetPublicHealthTriggerEventsToReviewAsync(int reviewId)
        {
            var returnable = await this.safetySurveillanceApplicationContext.PublicHealthToReviews
                .Where(trigger => trigger.PublicHealthReviewId == reviewId).OrderBy(o => o.TriggerSourceDataId)
                .ToListAsync() ?? new List<PublicHealthToReview>();
            return returnable;
        }

        public async Task<List<TriggerDetail>> GetPatientTriggerEventGroupings(List<string> facilities, DateRange dateRange, AdverseEventFilterData adverseEventFilterData, string userName,
            string type = SurveillanceTypeConstants.Harm, bool filterByAeDate = false)
        {
            Guard.Against.Null(facilities, nameof(facilities));

            List<TriggerDetail> triggers = await this.GetTriggerEventsWithReviewsAsync(facilities, dateRange, type, null, null, userName);


            triggers.ForEach(e => e.InitializeNullTriggerDetailProperties());

            if (type == SurveillanceTypeConstants.Harm)
            {
                triggers = await this.adverseEventFilterService.FilterTriggerResults(triggers, adverseEventFilterData,
                    userName);
            }

            return triggers;
        }

        public async Task<List<SeverityScaleItem>> GetSeverityScaleItems()
        {
            return await safetySurveillanceApplicationContext.SeverityScaleItems
                .Include(i => i.SeverityScaleItemCategory)
                .ToListAsync();
        }

        public async Task<List<TriggerDetail>> GetTriggersForGivenTimeFrame(DateRange dateRange,
            List<string> facilities)
        {
            List<HistoricalTriggerDetail> triggerEvents = await this.safetySurveillanceApplicationContext
                .HistoricalTriggerDetails
                .Include(c => c.Encounter)
                .Include(t => t.TriggerReference)
                .Include(tf => tf.TriggerFlags)
                .Where(triggerEvent =>
                    facilities.Contains(triggerEvent.Encounter.Location)
                    && triggerEvent.TriggerReference.SurveillanceType == "harm"
                    && triggerEvent.TriggerDts >= dateRange.StartDate && triggerEvent.TriggerDts <= dateRange.EndDate)
                .ToListAsync();

            return await this.triggerMatchService.MatchTriggerReviews(triggerEvents.ToTriggerDetailList(), dateRange, "harm", false, false);
        }
    }
}
