using EFCoreSecondLevelCacheInterceptor;
using SafetySurveillanceWebApi.Infrastructure;

namespace SafetySurveillanceWebApi.Repositories.Authorization;

using SafetySurveillanceWebApi.Models.ClientAdmin.SentinelEventEmailAddresses;
using Microsoft.EntityFrameworkCore;
using SafetySurveillanceWebApi.Data;
using SafetySurveillanceWebApi.Models.Authorize;

public class AuthorizationRepository : IAuthorizationRepository
{
    private readonly ISafetySurveillanceApplicationContext context;

    public AuthorizationRepository(ISafetySurveillanceApplicationContext context)
    {
        this.context = context;
    }

    public async Task<SafetyUser> GetUserByUserIdAsync(string userName)
    {
        var foundUser = await this.context.AuthorizeUsers
            .FirstOrDefaultAsync(user => user.UserName == userName);

        return foundUser;
    }

    public async Task<List<UserPermission>> GetUserPermissionsAsync(string userName)
    {
        var permissions = await this.context.AuthorizeUserPermissions.Include(p => p.Permissions)
            .Where(user => user.UserName == userName).ToListAsync();
        return permissions;
    }

    public async Task<List<GroupPermissions>> GetGroupPermissionsByUserIdAsync(string userName)
    {
        var groups = await this.GetGroupsByUserIdAsync(userName);

        var groupIds = groups.Select(g => g.GroupId).ToList();

        var permissions = await this.context.AuthorizeGroupPermissions.Include(p => p.Permission)
            .Where(g => groupIds.Contains(g.GroupId))
            .ToListAsync();

        return permissions;
    }

    public async Task<List<Group>> GetGroupsByUserIdAsync(string userName)
    {
        List<GroupUser> users = await this.context.AuthorizeGroupUsers
            .Cacheable(CacheExpirationMode.Sliding, TimeSpan.FromMinutes(3))
            .Where(user => user.UserName == userName)
            .Include(gu => gu.Group).Include(gu => gu.Group)
            .ThenInclude(group => group.GroupFacilityAccesses)
            .ThenInclude(facilityAccess => facilityAccess.ServiceLines)
            .Include(gu => gu.Group).Include(gu => gu.Group)
            .ThenInclude(group => group.GroupFacilityAccesses)
            .ThenInclude(facilityAccess => facilityAccess.Units).ToListAsync();

        var result = users.Select(gu => gu.Group).ToList();

        return result;
    }

    public async Task<bool> AddUserToGroupAsync(string userName, string groupId)
    {
        this.context.DetachAllEntities();
        this.context.AuthorizeGroupUsers.Add(new GroupUser {UserName = userName, GroupId = new Guid(groupId)});

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<List<Group>> GetGroups()
    {
        var groups = await this.context.AuthorizeGroups
            .Include(gu => gu.GroupUsers.Where(user => !user.User.IsDeleted).OrderBy(user => user.UserName))
            .OrderBy(g => g.GroupName)
            .Include(ag => ag.GroupPermissions)
            .ThenInclude(gp => gp.Permission)
            .ToListAsync();

        // TODO: Remove this and rely on the EF Join.
        // This is a temporary hot fix before we start using a HasConversion(t=>t.ToLower(), f=> f.ToLower())
        // for all the Username columns in the context.(This solution will require extensive testing across the app),
        // hence this temp fix
        var nullUsers = groups.SelectMany(c => c.GroupUsers).Where(g => g.User == null);
        if (nullUsers.Any())
        {
            var activeUsers = await this.context.AuthorizeUsers.Where(c => !c.IsDeleted).ToListAsync();
            foreach (var nullUser in nullUsers)
            {
                nullUser.User = activeUsers.FirstOrDefault(c => c.UserName == nullUser.UserName);
            }
        }

        return groups;
    }

    public async Task<List<SafetyUser>> GetUsers()
    {
        var users = await this.context.AuthorizeUsers.Include(up => up.UserPermissions)
            .ThenInclude(up => up.Permissions).Where(x => !x.IsDeleted).ToListAsync();

        return users;
    }

    public async Task<List<Permission>> GetPermissions()
    {
        var permissions = await this.context.AuthorizePermissions.ToListAsync();

        return permissions;
    }

    public async Task<GroupPermissions> AddPermissionToGroup(GroupPermissions groupPermissions)
    {
        this.context.AuthorizeGroupPermissions.Add(groupPermissions);

        await this.context.SaveChangesAsync();

        groupPermissions.Permission = await this.GetPermissionsByPermissionId(groupPermissions.PermissionId);

        return groupPermissions;
    }

    public async Task<bool> RemovePermissionFromGroup(GroupPermissions groupPermission)
    {
        this.context.AuthorizeGroupPermissions.Remove(groupPermission);

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<GroupPermissions> GetGroupPermissionsByGroupPermissionId(int groupPermissionsId)
    {
        return await this.context.AuthorizeGroupPermissions.FirstOrDefaultAsync(gp => gp.Id == groupPermissionsId);
    }

    public async Task<List<GroupPermissions>> GetGroupPermissionsByGroupId(Guid groupId)
    {
        return await this.context.AuthorizeGroupPermissions.Include(group => group.Permission)
            .Where(gp => gp.GroupId == groupId).ToListAsync();
    }

    public async Task<GroupUser> GetGroupUserByIdAsync(int groupUserId)
    {
        return await this.context.AuthorizeGroupUsers.FirstOrDefaultAsync(gu => gu.Id == groupUserId);
    }

    public async Task<List<GroupUser>> GetGroupUsersByGroupId(string groupId)
    {
        return await this.context.AuthorizeGroupUsers.Include(x => x.User)
            .Where(gu => gu.GroupId == Guid.Parse(groupId) && !gu.User.IsDeleted).OrderBy(x => x.UserName)
            .ToListAsync();
    }

    public async Task<List<GroupUser>> GetGroupsForUser(string username)
    {
        return await this.context.AuthorizeGroupUsers.Where(user => user.UserName == username).ToListAsync();
    }

    public async Task<bool> RemoveGroupUserByIdAsync(GroupUser groupUser)
    {
        this.context.AuthorizeGroupUsers.Remove(groupUser);

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<List<Group>> GetAuthorizationGroups()
    {
        return await this.context.AuthorizeGroups.OrderBy(g => g.GroupName).ToListAsync();
    }

    public async Task<Group> CreateGroup(Group group)
    {
        this.context.AuthorizeGroups.Add(group);
        await this.context.SaveChangesAsync();
        return await Task.FromResult(group);
    }

    public async Task<List<Group>> RemoveGroup(string groupId)
    {
        var group = this.context.AuthorizeGroups.SingleOrDefault(c => c.GroupId.ToString() == groupId);
        if (group != null)
        {
            group.IsDeleted = true;
        }

        await this.context.SaveChangesAsync();
        return await this.context.AuthorizeGroups.ToListAsync();
    }

    public async Task<Group> GetGroup(string groupId)
    {
        var group = await this.context.AuthorizeGroups.Where(c => c.GroupId.ToString() == groupId)
            .Include(group => group.GroupFacilityAccesses)
            .ThenInclude(groupAccess => groupAccess.ServiceLines)
            .Include(group => group.GroupFacilityAccesses).ThenInclude(groupAccess => groupAccess.Units)
            .Include(gu => gu.GroupUsers.Where(user => !user.User.IsDeleted).OrderBy(user => user.UserName))
            .ThenInclude(groupUsers => groupUsers.User)
            .Include(group => group.GroupPermissions).ThenInclude(gp => gp.Permission)
            .FirstOrDefaultAsync();

        if (group != null)
        {
            var nullUsers = group.GroupUsers.Where(g => g.User == null);

            if (nullUsers.Any())
            {
                var activeUsers = await this.context.AuthorizeUsers.Where(c => !c.IsDeleted).ToListAsync();
                foreach (var nullUser in nullUsers)
                {
                    nullUser.User = activeUsers.FirstOrDefault(c => c.UserName == nullUser.UserName);
                }
            }
        }

        return group;
    }

    public async Task<Group> UpdateGroup(Group group)
    {
        this.context.Update(group);
        if (!group.IsFacilityAccessGroup)
        {
            var facilityAccesses = await this.context.GroupsFacilityAccesses.Where(c => c.GroupId == group.GroupId)
                .ToListAsync();
            foreach (var groupsFacilityAccess in facilityAccesses)
            {
                this.context.GroupsFacilityAccesses.Remove(groupsFacilityAccess);
            }
        }

        await this.context.SaveChangesAsync();
        return await Task.FromResult(group);
    }

    public async Task<bool> RemoveFacilityAccessFromGroup(GroupsFacilityAccess facility)
    {
        this.context.GroupsFacilityAccesses.Remove(facility);

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> RemoveAllFacilityAccessFromGroup(Guid groupId)
    {
        List<GroupsFacilityAccess> existingAccesses = await this.context.GroupsFacilityAccesses
            .Where(access => access.GroupId == groupId).ToListAsync();

        this.context.GroupsFacilityAccesses.RemoveRange(existingAccesses.ToArray());

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<List<GroupsFacilityAccess>> GetGroupFacilityAccessByGroupId(string groupId)
    {
        var facilities = await this.context.GroupsFacilityAccesses
            .Where(facilityAccess => facilityAccess.GroupId == new Guid(groupId))
            .ToListAsync();

        return facilities;
    }

    public async Task<GroupsFacilityAccess> AddFacilityAccessForGroup(GroupsFacilityAccess facility)
    {
        this.context.GroupsFacilityAccesses.Add(facility);

        await this.context.SaveChangesAsync();

        return facility;
    }

    public async Task<GroupFacilityAccessServiceLines> AddFacilityReferenceLocationServiceLine(
        GroupFacilityAccessServiceLines serviceLine)
    {
        this.context.GroupFacilityAccessServiceLines.Add(serviceLine);

        await this.context.SaveChangesAsync();

        return serviceLine;
    }

    public async Task<GroupFacilityAccessUnits> AddFacilityReferenceLocationUnit(GroupFacilityAccessUnits unit)
    {
        this.context.GroupFacilityAccessUnits.Add(unit);

        await this.context.SaveChangesAsync();

        return unit;
    }

    private async Task<Permission> GetPermissionsByPermissionId(Guid permission)
    {
        return await this.context.AuthorizePermissions.Where(p => p.PermissionId == permission)
            .FirstOrDefaultAsync();
    }

    public async Task<bool> IsSentinelUser(string email)
    {
        return await this.context.SentinelEventEmailAddresses.Where(emails => emails.Email == email).AnyAsync();
    }

    public async Task<Group> GetGroupByName(string groupName)
    {
        return await context.AuthorizeGroups.FirstOrDefaultAsync(group =>
            group.GroupName.ToLower() == groupName.ToLower());
    }

    public async Task<SafetyUser> GetUserByEmail(string email)
    {
        return await this.context.AuthorizeUsers.FirstOrDefaultAsync(user => user.Email == email);
    }

    public async Task<List<SentinelEventEmailAddress>> GetUserSentinelEmailAssignments(SafetyUser user)
    {
        return await this.context.SentinelEventEmailAddresses.Where(sent => sent.Email == user.Email).ToListAsync();
    }

    public async Task<int> AddPermissionsToGroup(IEnumerable<GroupPermissions> permissions)
    {
        await this.context.AuthorizeGroupPermissions.AddRangeAsync(permissions);

        return await this.context.SaveChangesAsync();
    }

    public async Task<bool> GetUserInGroup(string userName, Guid groupId)
    {
        return await this.context.AuthorizeGroupUsers.AnyAsync(
            agu => agu.UserName == userName && agu.GroupId == groupId);
    }

    public async Task<bool> RemoveUserFromGroup(UserName userName, Guid groupId)
    {
        var groupUsers = await this.context.AuthorizeGroupUsers.Where(c => c.UserName == userName && c.GroupId == groupId).ToListAsync();
        this.context.AuthorizeGroupUsers.RemoveRange(groupUsers);
        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> UserHasPermission(string username, Guid permissionId)
    {
        return await this.context.AuthorizeGroupUsers
            .Where(user => user.UserName == username.ToLower())
            .Include(gu => gu.Group)
            .ThenInclude(group => group.GroupPermissions)
            .ThenInclude(groupPermissions => groupPermissions.Permission)
            .Where(gu => gu.Group.GroupPermissions.Select(x => x.PermissionId).Contains(permissionId))
            .AnyAsync();
    }

    public async Task<List<SafetyUser>> GetUsersWithPermission(Guid permissionId)
    {
        var users = await this.context.AuthorizeGroupUsers
            .Include(gu => gu.Group)
            .ThenInclude(group => group.GroupPermissions)
            .ThenInclude(groupPermissions => groupPermissions.Permission)
            .Where(gu => gu.Group.GroupPermissions.Select(x => x.PermissionId).Contains(permissionId))
            .Select(x => x.User)
            .Where(x => !x.IsDeleted)
            .ToListAsync();

        return users.DistinctBy(x => x.UserName).ToList();
    }

    public async Task<List<SafetyUser>> GetUsersByIncidentLocationLead(string location, string facility)
    {
        var users = await (
            from authUsers in this.context.AuthorizeUsers
            join userLocations in this.context.AuthorizeUserLocations on authUsers.UserName equals userLocations.UserName
            where userLocations.Location == location
                && userLocations.Facility == facility
            select authUsers)
            .ToListAsync();

        return users;
    }

    public async Task<List<SafetyUser>> GetUsersByIncidentCategoryLead(string category)
    {
        var users = await (
                from user in this.context.AuthorizeUsers
                join userCategory in this.context.AuthorizeUserCategories on user.UserName equals userCategory.UserName
                join adverseCategory in this.context.AdverseCategories on userCategory.CategoryId equals adverseCategory.CategoryId
                where adverseCategory.AdverseEventCategoryDsc == category
                select user)
            .ToListAsync();

        return users;
    }

    public async Task<bool> UserHasFacilityAccess(string username, string facility)
    {
        return await this.context.AuthorizeGroupUsers
            .Where(user => user.UserName == username.ToLower())
            .Include(gu => gu.Group)
            .ThenInclude(group => group.GroupFacilityAccesses)
            .SelectMany(mf => mf.Group.GroupFacilityAccesses.Select(c => c.Facility))
            .AnyAsync(facilityAccess => facilityAccess == facility);
    }
}
