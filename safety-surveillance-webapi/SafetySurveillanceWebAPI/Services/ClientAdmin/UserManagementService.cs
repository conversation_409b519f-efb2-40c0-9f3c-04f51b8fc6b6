using System;
using System.Collections.Immutable;
using Microsoft.EntityFrameworkCore;
using SafetySurveillanceWebApi.Data;
using SafetySurveillanceWebApi.Dto;
using SafetySurveillanceWebApi.Models.Authorize;
using SafetySurveillanceWebApi.Models.ClientAdmin.UserManagement;
using SafetySurveillanceWebApi.Services.Authorization;

namespace SafetySurveillanceWebApi.Services.ClientAdmin
{
    public class UserManagementService : IUserManagementService
    {
        private readonly ISafetySurveillanceApplicationContext context;
        private readonly IGroupService groupService;

        public UserManagementService(
            ISafetySurveillanceApplicationContext context,
            IGroupService groupService)
        {
            this.context = context;
            this.groupService = groupService;
        }

        public async Task UpsertBulkUploadUsers(List<UserCreateDto> users, string createdBy)
        {
            var addUserDtos = await ConvertToAddUserDtos(users);
            await this.UpsertUsers(addUserDtos, createdBy);
        }

        private async Task<List<AddUserInputDto>> ConvertToAddUserDtos(List<UserCreateDto> users)
        {
            var groups = context.AuthorizeGroups.ToDictionary(g => g.GroupName.ToLower());
            var categories = await context.AdverseCategories.ToDictionaryAsync(g => g.AdverseEventCategoryDsc.ToLower());
            var locations = await context.ReferenceLocationUnits.ToListAsync();
            var customLocations = await context.CustomLocations.ToListAsync();

            var allLocations = locations
                .Select(c => new { Facility = c.Location, Location = c.Department })
                .ToList()
                .Concat(customLocations.Select(c => new { Facility = c.Facility, Location = c.Name + " (custom location)" }).ToList());

            var result =users.Select(c =>
            {
                return new AddUserInputDto()
                {
                    UserName = c.UserName,
                    FirstName = c.FirstName,
                    LastName = c.LastName,
                    Email = c.Email,
                    FacilityGroups = groups.Where(d => d.Key == c.FacilityGroup.ToLower()).Select(d => d.Value.GroupId).ToList(),
                    RoleGroups = groups.Where(d => d.Key == c.Role.ToLower()).Select(d => d.Value.GroupId).ToList(),
                    Locations =
                        allLocations
                            .Where(p => c.AssignedIncidentFacility.ToLower() == p.Facility.ToLower() && c.AssignedIncidentLocation.ToLower() == p.Location.ToLower())
                            .Select(d => new IncidentLocationLeadDto { Facility = d.Facility, Location = d.Location })
                            .ToList(),
                    Categories = categories
                        .Where(d => d.Key == c.AssignedIncidentCategory.ToLower())
                        .Select(kv => kv.Value.CategoryId)
                        .ToList()
                };
            }).ToList();

            return await Task.FromResult(result);
        }

        public async Task UpsertUsers(List<AddUserInputDto> users, string createdBy)
        {
            foreach (var userDto in users.Where(u => u.Errors.Count == 0))
            {
                await UpsertUser(createdBy, userDto);
            }
        }

        private async Task UpsertUser(string createdBy, AddUserInputDto userDto)
        {
            try
            {
                var existingUser = await context.AuthorizeUsers.FindAsync((UserName)userDto.UserName);
                var isNew = MapUser(userDto, ref existingUser, createdBy);
                if (isNew)
                {
                    context.AuthorizeUsers.Add(existingUser);
                    foreach (var facilityGroupId in userDto.FacilityGroups)
                    {
                        context.AuthorizeGroupUsers.Add(
                            new GroupUser { UserName = userDto.UserName, GroupId = facilityGroupId });
                    }

                    foreach (var roleGroupId in userDto.RoleGroups)
                    {
                        context.AuthorizeGroupUsers.Add(
                            new GroupUser { UserName = userDto.UserName, GroupId = roleGroupId });
                    }

                    foreach (var location in userDto.Locations)
                    {
                        context.AuthorizeUserLocations.Add(
                            new IncidentLocationLead()
                            {
                                Location = location.Location,
                                UserName = userDto.UserName,
                                Facility = location.Facility
                            });
                    }

                    foreach (var category in userDto.Categories)
                    {
                        context.AuthorizeUserCategories.Add(
                            new IncidentCategoryLead()
                            {
                                UserName = userDto.UserName,
                                CategoryId = category
                            });
                    }
                }
                else
                {
                    existingUser.IsDeleted = false;
                    context.AuthorizeUsers.Update(existingUser);
                    foreach (var facilityGroupId in userDto.FacilityGroups)
                    {
                        await groupService.AddUserToGroup(existingUser.UserName,
                            facilityGroupId.ToString());
                    }

                    foreach (var roleGroupId in userDto.RoleGroups)
                    {
                        await groupService.AddUserToGroup(existingUser.UserName,
                            roleGroupId.ToString());
                    }
                    foreach (var location in userDto.Locations)
                    {
                        context.AuthorizeUserLocations.Add(
                            new IncidentLocationLead()
                            {
                                Location = location.Location,
                                UserName = userDto.UserName,
                                Facility = location.Facility
                            });
                    }

                    foreach (var category in userDto.Categories)
                    {
                        context.AuthorizeUserCategories.Add(
                            new IncidentCategoryLead()
                            {
                                UserName = userDto.UserName,
                                CategoryId = category
                            });
                    }
                }

                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                userDto.Errors.Add($"Unexpected error: {ex.Message}");
            }
        }

        public async Task EditUser(AddUserInputDto user, string modifiedBy)
        {
            if (user.Errors.Count == 0)
            {
                var existingUser = await context.AuthorizeUsers.FindAsync((UserName)user.UserName);

                if (existingUser == null)
                {
                    user.Errors.Add("Error: Cannot find user to edit.");
                }
                else
                {
                    existingUser.Email = user.Email;
                    existingUser.DisplayName = $"{user.FirstName} {user.LastName}";
                    existingUser.LastUpdatedDateTime = DateTime.Now;
                    existingUser.LastUpdatedBy = modifiedBy;

                    var groupUsers = await this.groupService.GetGroupsForUser(user.UserName);

                    context.AuthorizeGroupUsers.RemoveRange(groupUsers);

                    foreach (var facilityGroupId in user.FacilityGroups)
                    {
                        context.AuthorizeGroupUsers.Add(
                            new GroupUser {UserName = user.UserName, GroupId = facilityGroupId});
                    }

                    foreach (var roleGroupId in user.RoleGroups)
                    {
                        context.AuthorizeGroupUsers.Add(
                            new GroupUser {UserName = user.UserName, GroupId = roleGroupId});
                    }
                    
                    // delete existing locations
                    var existingLocations = await
                        context.AuthorizeUserLocations.Where(l => l.UserName == existingUser.UserName).ToListAsync();
                    
                    context.AuthorizeUserLocations.RemoveRange(existingLocations);
                    
                    // delete existing categories
                    var existingCategories = await
                        context.AuthorizeUserCategories.Where(c => c.UserName == existingUser.UserName).ToListAsync();
                    context.AuthorizeUserCategories.RemoveRange(existingCategories);
                    
                    foreach (var location in user.Locations)
                    {
                        context.AuthorizeUserLocations.Add(
                            new IncidentLocationLead()
                            {
                                Location = location.Location,
                                UserName = user.UserName,
                                Facility = location.Facility
                            });
                    }
                        
                    foreach (var category in user.Categories)
                    {
                        context.AuthorizeUserCategories.Add(
                            new IncidentCategoryLead()
                            {
                                UserName = user.UserName,
                                CategoryId = category
                            });
                    }
                    
                    await context.SaveChangesAsync();
                }
            }
        }

        public async Task SetUserLoginDate(string userName)
        {
            var existingUser = await context.AuthorizeUsers.FindAsync(new UserName(userName));
            existingUser.LastLoginDateTime = DateTime.Now;
            await context.SaveChangesAsync();
        }

        private void InsertUser(SafetyUser user, Guid facilityGroupId, Guid roleGroupId)
        {
            context.AuthorizeUsers.Add(user);
            context.AuthorizeGroupUsers.Add(
                new GroupUser {UserName = user.UserName, GroupId = facilityGroupId});
            context.AuthorizeGroupUsers.Add(
                new GroupUser {UserName = user.UserName, GroupId = roleGroupId});
        }

        private bool MapUser(IUserCreateDto userDto, ref SafetyUser user, string createdBy)
        {
            var isNew = false;
            if (user == null)
            {
                user = new SafetyUser();
                isNew = true;
            }

            user.UserName = isNew ? userDto.UserName: user.UserName;
            user.DisplayName = $"{userDto.FirstName} {userDto.LastName}";
            user.Email = userDto.Email;
            user.IdentityProvider = "Windows";
            user.CreatedBy = isNew ? createdBy : user.CreatedBy;
            user.CreatedDateTime = isNew ? DateTime.Now : user.CreatedDateTime;
            user.LastUpdatedBy = createdBy;
            user.LastUpdatedDateTime = DateTime.Now;

            return isNew;
        }
    }

    public interface IUserManagementService
    {
        Task UpsertBulkUploadUsers(List<UserCreateDto> users, string createdBy);

        Task UpsertUsers(List<AddUserInputDto> users, string createdBy);

        Task EditUser(AddUserInputDto user, string modifiedBy);

        Task SetUserLoginDate(string userName);
    }
}
