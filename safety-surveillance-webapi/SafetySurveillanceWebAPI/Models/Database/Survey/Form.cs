namespace SafetySurveillanceWebApi.Models;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

[Table("Form", Schema = "Survey")]
public class Form
{
    [Key, Column("FormID")]
    public Guid FormId { get; set; }

    [Column("FormNM")]
    public string Name { get; set; }

    [Column("FormTypeTXT")]
    public string Type { get; set; }

    [Column("CreatedDTS")]
    public DateTime CreatedDate { get; set; }

    [Column("CreatedByTXT")]
    public string CreatedBy { get; set; }

    [Column("ModifiedDTS")]
    public DateTime? ModifiedDate { get; set; }

    [Column("IsActiveFLG")]
    public bool IsActive { get; set; }

    [Column("ParentFormID")]
    public Guid? ParentFormId { get; set; }

    public List<Section> Sections { get; set; }

    [Column("Version")]
    public int Version { get; set; }

    [JsonIgnore]
    [ForeignKey("ParentFormId")]
    public Form ParentForm { get; set; }

    [NotMapped]
    public List<Form> ChildForms { get; set; }
}
