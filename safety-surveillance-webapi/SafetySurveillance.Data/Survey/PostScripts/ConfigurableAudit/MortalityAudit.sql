DECLARE @AuditId UNIQUEIDENTIFIER = 'e8f03e4e-426b-4dfb-917d-fd60c5c071c8'
DECLARE @MortalityFormId uniqueidentifier = '5e8f1c05-0deb-4543-8d3a-a1ed31887e39'
declare @CreatedBy varchar(100) = 'Health Catalyst'

-- Configurable Mortality Audit ========================================================================================
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditTool] where Id = @AuditId))
BEGIN
  insert into [Survey].[AuditTool] (
    Id,
    Name,
    GroupedColumnName,
    NumOfDocuments,
    NumOfDocumentsRange,
    FormId,
    CountByGroup,
    CountLabelName,
    CreatedDTS,
    CreatedByTXT,
    IsActiveFLG
  ) values (
    @AuditId,
    'Configurable Mortality Audit',
    null,
    1, --NumOfDocuments
    null,
    @MortalityFormId,
    0,
    'Patient Count',
    sysdatetime(),
    @CreatedBy,
    1
  );
END

-- Patient List ========================================================================================================

-- Patient Column
declare @patientColumnId uniqueidentifier = 'ef67ca68-3289-463c-9865-1de74daa38aa'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @patientColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @patientColumnId, @AuditId, 'PatientFullName', 'Patient', 1, 'text', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'CONCAT(PatientLastNM, '', '', PatientFirstNM)', null, sysdatetime(),
    @CreatedBy, 1, 1, 1
  )
END

-- MRN hidden column for Patient
declare @mrnColumnId uniqueidentifier = 'b3e4a176-94a9-4ba5-a232-34465bcdfd1c'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @mrnColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @mrnColumnId, @AuditId, 'Mrn', 'MRN', 1, 'text', 0,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'MRN', null, sysdatetime(),
    @CreatedBy, 0, 0, 0
  )
END

-- Visit Number hidden column for Patient
declare @visitNumberColumnId uniqueidentifier = 'a7e7efc2-2995-46c8-a235-893f087e4bfc'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @visitNumberColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @visitNumberColumnId, @AuditId, 'VisitNumber', 'Visit#', 1, 'text', 0,
  'asc', 'Survey', 'MortalityAuditWithEncounter', 'FacilityAccountID', null, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- Location Column
declare @locationColumnId uniqueidentifier = 'efba36bc-5a85-403b-bc9a-d548200883c5'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @locationColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @locationColumnId, @AuditId, 'Location', 'Location', 2, 'text', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'Location', null, sysdatetime(),
    @CreatedBy, 1, 1, 1
  )
END

-- Age Column
declare @ageColumnId uniqueidentifier = '42691f4a-c792-4bb8-81a3-6f7fedea75af'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @ageColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @ageColumnId, @AuditId, 'AgeInMonths', 'Age', 3, 'Age', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'DATEDIFF(month, BirthDTS, DeathDTS)', 'yrs', sysdatetime(),
    @CreatedBy, 1, 1, 1
  )
END

-- Type Column
declare @typeColumnId uniqueidentifier = '09a365b3-d78b-42d7-86a3-c446003013a6'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @typeColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @typeColumnId, @AuditId, 'Type', 'Type', 4, 'text', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'Type', null, sysdatetime(),
    @CreatedBy, 1, 1, 1
  )
END

-- Primary Diagnosis Column
declare @primaryDiagnosisColumnId uniqueidentifier = '155e723b-4daf-4e93-8114-78af43e54ac7'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @primaryDiagnosisColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @primaryDiagnosisColumnId, @AuditId, 'PrimaryDiagnosis', 'Primary Diagnosis', 5, 'text', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'PrimaryDiagnosisDSC', null, sysdatetime(),
    @CreatedBy, 1, 1, 1
  )
END

-- Admit Date Column
declare @admitDateColumnId uniqueidentifier = '3b0576fb-8f38-4b22-a86e-01b5b90d6f99'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @admitDateColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @admitDateColumnId, @AuditId, 'AdmitDate', 'Admit Date', 6, 'Date', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'AdmitDTS', NULL, sysdatetime(),
    @CreatedBy, 0, 0, 1
  )
END

-- Deceased Date Column
declare @deceasedDateColumnId uniqueidentifier = '539d0819-a7b6-4ce7-8788-f5fd3453b1dc'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @deceasedDateColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @deceasedDateColumnId, @AuditId, 'DeceasedDate', 'Deceased Date', 7, 'Date', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'DeathDTS', NULL, sysdatetime(),
    @CreatedBy, 0, 0, 1
  )
END

-- Audit
declare @auditColumnId uniqueidentifier = 'bbab88a4-d43b-447a-9477-fe67e55943a9'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @auditColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @auditColumnId, @AuditId, 'Action', 'Audit', 8, 'Action', 10,
    'asc', NULL, NULL, NULL, '+ new', sysdatetime(),
    @CreatedBy, 0, 0, 1
  )
END

-- Updated
declare @UpdatedColumnId uniqueidentifier = 'd7b0e2a4-c9c0-405c-beb9-f699c09d2d2b'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @UpdatedColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @UpdatedColumnId, @AuditId, 'Updated', 'Updated', 9, 'Date', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'Updated', NULL, sysdatetime(),
    @CreatedBy, 0, 0, 1
  )
END

-- Status
declare @statusColumnId uniqueidentifier = 'efef3fc3-385a-4426-a186-4a08b3d55a18'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @statusColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @statusColumnId, @AuditId, 'Status', 'Status', 10, 'Text', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'StatusDSC', NULL, sysdatetime(),
    @CreatedBy, 0, 0, 1
  )
END

-- Assignee
declare @AssigneeColumnId uniqueidentifier = 'f8a560cd-583b-4467-94f6-6e7ef608abf6'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @AssigneeColumnId))
BEGIN
  insert into [Survey].[AuditToolColumn] (
    Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
    SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
    CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
  ) values (
    @AssigneeColumnId, @AuditId, 'Assignee', 'Assignee', 11, 'Text', 10,
    'asc', 'Survey', 'MortalityAuditWithEncounter', 'AssignedToUser', NULL, sysdatetime(),
    @CreatedBy, 0, 0, 1
  )
END

-- Assignee UserName
declare @AssigneeUserNameColumnId uniqueidentifier = '10667186-d694-4855-80b4-b4670908b29e'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @AssigneeUserNameColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @AssigneeUserNameColumnId, @AuditId, 'AssigneeUserName', 'Assignee UserNAme', 11, 'Text', 10,
  'asc', 'Survey', 'MortalityAuditWithEncounter', 'AssignedToUserName', NULL, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- Hidden columns necessary for table display
-- Facility
declare @FacilityColumnId uniqueidentifier = '6783b64a-53d5-410f-8673-451fb940ae28'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @FacilityColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @FacilityColumnId, @AuditId, 'Facility', 'Facility', 12, 'Text', 10,
  'asc', 'Survey', 'MortalityAuditWithEncounter', 'Facility', NULL, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- Service Line
declare @ServiceLineColumnId uniqueidentifier = '3309f423-59a0-4678-9657-0dbe3d2676e5'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @ServiceLineColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @ServiceLineColumnId, @AuditId, 'ServiceLine', 'Service Line', 13, 'Text', 10,
  'asc', 'Survey', 'MortalityAuditWithEncounter', 'PrimaryServiceLineNM', NULL, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- SurveyEventId
declare @SurveyEventIdColumnId uniqueidentifier = '9d2dae14-99ed-4b11-8fed-f06d06f8b371'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @SurveyEventIdColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @SurveyEventIdColumnId, @AuditId, 'SurveyEventId', 'SurveyEventId', 14, 'Text', 10,
  'asc', 'Survey', 'MortalityAuditWithEncounter', 'SurveyEventId', NULL, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- EncounterId
declare @EncounterIdColumnId uniqueidentifier = '2f649553-5fd3-448f-b511-28f3a02b1206'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @EncounterIdColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @EncounterIdColumnId, @AuditId, 'EncounterId', 'EncounterId', 15, 'Text', 10,
  'asc', 'Survey', 'MortalityAuditWithEncounter', 'EncounterID', NULL, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- EncounterSource
declare @EncounterSrcColumnId uniqueidentifier = 'd72263c6-5fe5-4fed-a1ad-b8a156626dcc'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @EncounterSrcColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @EncounterSrcColumnId, @AuditId, 'EncounterSrc', 'EncounterSrc', 16, 'Text', 10,
  'asc', 'Survey', 'MortalityAuditWithEncounter', 'EncounterSourceDSC', NULL, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- Create Filters ======================================================================================================
declare @FacilityFilterId uniqueidentifier = '215c295f-5773-44b3-9102-9c7e9b2d9089'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @FacilityFilterId))
BEGIN
  insert into [Survey].[AuditToolFilter] (
    Id, AuditId, Name, FilterClass,
    FilterCriteria, FilterCriteriaPropertyName,
    CreatedDTS, CreatedByTXT
  ) values (
    @FacilityFilterId, @AuditId, 'Facility', 'Facility',
    'CriteriaFacility', 'Facility',
    sysdatetime(), @CreatedBy
  )
END

declare @UnitFilterId uniqueidentifier = '4bb8748e-d290-4ad2-8998-79c3140f4d93'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @UnitFilterId))
BEGIN
insert into [Survey].[AuditToolFilter] (
  Id, AuditId, Name, FilterClass,
  FilterCriteria, FilterCriteriaPropertyName,
  CreatedDTS, CreatedByTXT
) values (
  @UnitFilterId, @AuditId, 'Unit', 'Unit',
  'CriteriaUnit', 'Unit',
  sysdatetime(), @CreatedBy
  )
END

declare @ServiceLineFilterId uniqueidentifier = 'ca86b4f2-528a-477c-92e1-bed4ed4a5b98'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @ServiceLineFilterId))
BEGIN
insert into [Survey].[AuditToolFilter] (
  Id, AuditId, Name, FilterClass,
  FilterCriteria, FilterCriteriaPropertyName,
  CreatedDTS, CreatedByTXT
) values (
  @ServiceLineFilterId, @AuditId, 'Service Line', 'ServiceLine',
  'CriteriaServiceLine', 'ServiceLine',
  sysdatetime(), @CreatedBy
  )
END

declare @AssigneeFilterId uniqueidentifier = '63d57037-8a4a-41f4-b731-a9d63643cb1d'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @AssigneeFilterId))
BEGIN
insert into [Survey].[AuditToolFilter] (
  Id, AuditId, Name, FilterClass,
  FilterCriteria, FilterCriteriaPropertyName,
  CreatedDTS, CreatedByTXT
) values (
  @AssigneeFilterId, @AuditId, 'Assignee', 'Assignee',
  'CriteriaAssignee', 'AssigneeUserName',
  sysdatetime(), @CreatedBy
  )
END

declare @StatusFilterId uniqueidentifier = '18b930df-140a-432e-8a1a-126170a7b298'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @StatusFilterId))
BEGIN
insert into [Survey].[AuditToolFilter] (
  Id, AuditId, Name, FilterClass,
  FilterCriteria, FilterCriteriaPropertyName,
  CreatedDTS, CreatedByTXT
) values (
  @StatusFilterId, @AuditId, 'Status', 'Status',
  'CriteriaStatus', 'Status',
  sysdatetime(), @CreatedBy
  )
END

-- Data Export Columns =================================================================================================
-- Patient Column
declare @patientNameExportColumnId uniqueidentifier = '2ebc1b96-5077-424c-a2b5-28a9aa5815b5'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @patientNameExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @patientNameExportColumnId, @AuditId, 'PatientFullName', 'Patient Name', 'Text', 'Demographics Information',
    1, 'Survey', 'MortalityAuditWithEncounter', 'CONCAT(PatientLastNM, '', '', PatientFirstNM)', sysdatetime(), @CreatedBy
  )
END

-- MRN Column
declare @MrnExportColumnId uniqueidentifier = 'c4f1d2f5-3f33-4813-8876-9a6d63b97725'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @MrnExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @MrnExportColumnId, @AuditId, 'Mrn', 'MRN', 'Text', 'Demographics Information',
    2, 'Survey', 'MortalityAuditWithEncounter', 'MRN', sysdatetime(), @CreatedBy
  )
END

-- Gender Column
declare @GenderExportColumnId uniqueidentifier = 'd6095cc7-43b4-4e26-9ffd-1692ae767d75'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @GenderExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @GenderExportColumnId, @AuditId, 'Gender', 'Gender', 'Text', 'Demographics Information',
    3, 'Survey', 'MortalityAuditWithEncounter', 'Gender', sysdatetime(), @CreatedBy
  )
END

-- Date of Birth Column
declare @DateOfBirthExportColumnId uniqueidentifier = '5f471f30-fd88-483b-9d8f-76ceb511bc43'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @DateOfBirthExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @DateOfBirthExportColumnId, @AuditId, 'BirthDTS', 'Date Of Birth', 'Date', 'Demographics Information',
    4, 'Survey', 'MortalityAuditWithEncounter', 'BirthDTS', sysdatetime(), @CreatedBy
  )
END

-- Age Column
declare @AgeExportColumnId uniqueidentifier = '4a9b6fb2-5ffd-4220-8e3c-ced528552938'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AgeExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AgeExportColumnId, @AuditId, 'Age', 'Age', 'text', 'Demographics Information',
    5, 'Survey', 'MortalityAuditWithEncounter', 'FLOOR(DATEDIFF(month, BirthDTS, DeathDTS) / 12)', sysdatetime(), @CreatedBy
  )
END

-- Visit Number Column
declare @VisitNumberExportColumnId uniqueidentifier = 'e252a7eb-2d9b-4d4b-b838-e7e74e92d45c'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @VisitNumberExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @VisitNumberExportColumnId, @AuditId, 'FacilityAccountID', 'Visit Number', 'Text', 'Encounter Information',
    6, 'Survey', 'MortalityAuditWithEncounter', 'FacilityAccountID', sysdatetime(), @CreatedBy
  )
END

-- Admit Unit Column
declare @AdmitUnitExportColumnId uniqueidentifier = '4879e0e0-f156-4727-aa1d-266247815d57'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AdmitUnitExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AdmitUnitExportColumnId, @AuditId, 'AdmitDepartmentNM', 'Admit Unit', 'Text', 'Encounter Information',
    7, 'Survey', 'MortalityAuditWithEncounter', 'AdmitDepartmentNM', sysdatetime(), @CreatedBy
  )
END

-- Admit Date/Time Column
declare @AdmitDateExportColumnId uniqueidentifier = '67cde6e1-82d5-48b2-98a2-0a85c3c6e53e'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AdmitDateExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AdmitDateExportColumnId, @AuditId, 'AdmitDTS', 'Admit Date/Time', 'Date', 'Encounter Information',
    8, 'Survey', 'MortalityAuditWithEncounter', 'AdmitDTS', sysdatetime(), @CreatedBy
  )
END

-- Inpatient Admit Date/Time Column
declare @InpatientAdmitDateExportColumnId uniqueidentifier = '32c20bca-afb9-462e-af19-4c8d13df0be5'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @InpatientAdmitDateExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @InpatientAdmitDateExportColumnId, @AuditId, 'InpatientAdmitDTS', 'Inpatient Admit Date/Time', 'Date', 'Encounter Information',
    9, 'Survey', 'MortalityAuditWithEncounter', 'InpatientAdmitDTS', sysdatetime(), @CreatedBy
  )
END

-- Admit Source Column
declare @AdmitSourceExportColumnId uniqueidentifier = '290c00f3-6907-42b0-bad0-bb06860b6eeb'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AdmitSourceExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AdmitSourceExportColumnId, @AuditId, 'AdmitSourceDSC', 'Admit Source', 'Text', 'Encounter Information',
    10, 'Survey', 'MortalityAuditWithEncounter', 'AdmitSourceDSC', sysdatetime(), @CreatedBy
  )
END

-- Primary Diagnosis Description Column
declare @PrimaryDiagnosisDscExportColumnId uniqueidentifier = '4887ca88-8378-4ee0-9cea-15c818599d6b'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @PrimaryDiagnosisDscExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @PrimaryDiagnosisDscExportColumnId, @AuditId, 'PrimaryDiagnosisDSC', 'Primary Diagnosis Description', 'Text', 'Encounter Information',
    11, 'Survey', 'MortalityAuditWithEncounter', 'PrimaryDiagnosisDSC', sysdatetime(), @CreatedBy
  )
END

-- Service Column
declare @ServiceExportColumnId uniqueidentifier = 'e68e12ec-f296-4ff0-a65e-b5713d26a3cd'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @ServiceExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @ServiceExportColumnId, @AuditId, 'PrimaryServiceLineNM', 'Service', 'Text', 'Encounter Information',
    12, 'Survey', 'MortalityAuditWithEncounter', 'PrimaryServiceLineNM', sysdatetime(), @CreatedBy
  )
END

-- Attending Provider Column
declare @AttendingProviderExportColumnId uniqueidentifier = '52f0cbf6-dfab-4c7e-aac7-137d12460aa1'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AttendingProviderExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AttendingProviderExportColumnId, @AuditId, 'AttendingProviderFullNM', 'Attending Provider', 'Text', 'Encounter Information',
    13, 'Survey', 'MortalityAuditWithEncounter', 'AttendingProviderFullNM', sysdatetime(), @CreatedBy
  )
END

-- Deceased Date/Time Column
declare @DeceasedDateTimeExportColumnId uniqueidentifier = 'fb5f7c01-fa35-4eb7-8a0c-025a8aaec6c9'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @DeceasedDateTimeExportColumnId))
BEGIN
insert into [Survey].[AuditToolExport] (
  Id, AuditId, ColumnName, DisplayName, DataType, Section,
  DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
) values (
  @DeceasedDateTimeExportColumnId, @AuditId, 'DeceasedDts', 'Deceased Date/Time', 'Text', 'Encounter Information',
  14, 'Survey', 'MortalityAuditWithEncounter', 'DeathDTS', sysdatetime(), @CreatedBy
  )
END

-- Deceased Unit Column
declare @DeceasedUnitExportColumnId uniqueidentifier = '19ec2146-763d-4095-8c6d-388dcfab5847'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @DeceasedUnitExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @DeceasedUnitExportColumnId, @AuditId, 'Location', 'Deceased Unit', 'Text', 'Encounter Information',
    15, 'Survey', 'MortalityAuditWithEncounter', 'Location', sysdatetime(), @CreatedBy
  )
END

-- Principal Procedure Column
declare @PrincipalProcedureExportColumnId uniqueidentifier = '3c5af66e-3bb3-49bb-b3b5-92d96264dc66'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @PrincipalProcedureExportColumnId))
BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @PrincipalProcedureExportColumnId, @AuditId, 'RecentSurgicalProcedureDSC', 'Principal Procedure', 'Text', 'Encounter Information',
    16, 'Survey', 'MortalityAuditWithEncounter', 'RecentSurgicalProcedureDSC', sysdatetime(), @CreatedBy
  )
END

-- Principal Procedure Date/Time Column
declare @PrincipalProcedureDtsExportColumnId uniqueidentifier = '873c24de-3f50-4cb0-aecd-d1e681cc5594'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @PrincipalProcedureDtsExportColumnId))
BEGIN
insert into [Survey].[AuditToolExport] (
  Id, AuditId, ColumnName, DisplayName, DataType, Section,
  DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
) values (
  @PrincipalProcedureDtsExportColumnId, @AuditId, 'RecentSurgicalProcedureDTS', 'Principal Procedure Date/Time', 'Text', 'Encounter Information',
  17, 'Survey', 'MortalityAuditWithEncounter', 'RecentSurgicalProcedureDTS', sysdatetime(), @CreatedBy
  )
END

-- Previous Discharge Date Column
declare @PreviousDischargeDtsExportColumnId uniqueidentifier = '1d8777ab-a655-4db0-aeb1-f087e4067d97'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @PreviousDischargeDtsExportColumnId))
BEGIN
insert into [Survey].[AuditToolExport] (
  Id, AuditId, ColumnName, DisplayName, DataType, Section,
  DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
) values (
  @PreviousDischargeDtsExportColumnId, @AuditId, 'PreviousDischargeDTS', 'Previous Discharge Date', 'Text', 'Encounter Information',
  18, 'Survey', 'MortalityAuditWithEncounter', 'PreviousDischargeDTS', sysdatetime(), @CreatedBy
  )
END

-- Patient Details Columns =============================================================================================
declare @PatientDetailsColumnId uniqueidentifier = '591614a9-ebd4-4452-b7e6-d6d2bfbc3360'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @PatientDetailsColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @PatientDetailsColumnId, @AuditId, 'PatientFullName', 'Patient',
    1, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'CONCAT(PatientLastNM, '', '', PatientFirstNM)', SYSDATETIME(), @CreatedBy
  )
END

-- MRN
declare @MRNDetailsColumnId uniqueidentifier = 'f169c2f2-5135-4a65-92f1-86e9647556bf'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @MRNDetailsColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @MRNDetailsColumnId, @AuditId, 'Mrn', 'Patient',
    2, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'MRN', SYSDATETIME(), @CreatedBy
  )
END

-- Visit Number
declare @VisitNumberPatientDetailColumnId uniqueidentifier = '1acb76a0-7bcc-48ce-90c8-b8bad71bf672'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @VisitNumberPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @VisitNumberPatientDetailColumnId, @AuditId, 'VisitNumber', 'Visit #',
    3, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'FacilityAccountID', SYSDATETIME(), @CreatedBy
  )
END

-- Gender
declare @GenderPatientDetailColumnId uniqueidentifier = '4d3e991c-0175-447f-9d95-cd4b84028bee'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @GenderPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @GenderPatientDetailColumnId, @AuditId, 'Gender', 'Gender',
    4, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'Gender', SYSDATETIME(), @CreatedBy
  )
END

-- Date of Birth
declare @DateOfBirthPatientDetailColumnId uniqueidentifier = 'bfc3c40b-6f28-4255-941e-b0fe37c18234'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @DateOfBirthPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @DateOfBirthPatientDetailColumnId, @AuditId, 'BirthDts', 'Date of Birth',
    5, 'Date', 'Survey', 'MortalityAuditWithEncounter',
    'BirthDTS', SYSDATETIME(), @CreatedBy
  )
END

-- Age
declare @AgePatientDetailColumnId uniqueidentifier = 'ecd770fc-01dc-4126-9893-2e1c0acf0c18'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @AgePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AgePatientDetailColumnId, @AuditId, 'AgeInMonths', 'Age',
    6, 'Age', 'Survey', 'MortalityAuditWithEncounter',
    'DATEDIFF(month, BirthDTS, AdmitDTS)', SYSDATETIME(), @CreatedBy
  )
END

-- Race
declare @RacePatientDetailColumnId uniqueidentifier = '6b683647-ab6b-4424-b45c-68dfabe99a6b'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @RacePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @RacePatientDetailColumnId, @AuditId, 'Race', 'Race',
    7, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'RaceDSC', SYSDATETIME(), @CreatedBy
  )
END

-- Ethnicity
declare @EthnicityPatientDetailColumnId uniqueidentifier = '6b683647-ab6b-4424-b45c-68dfabe99a6b'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @EthnicityPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @EthnicityPatientDetailColumnId, @AuditId, 'Ethnicity', 'Ethnicity',
    8, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'EthnicGroupDSC', SYSDATETIME(), @CreatedBy
  )
END

-- Preferred Language
declare @LanguagePatientDetailColumnId uniqueidentifier = '5e351209-579d-4a9a-97e5-89d79e8e22dd'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @LanguagePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @LanguagePatientDetailColumnId, @AuditId, 'Language', 'Preferred Language',
    9, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'LanguageDSC', SYSDATETIME(), @CreatedBy
  )
END

-- Marital Status
declare @MaritalStatusPatientDetailColumnId uniqueidentifier = 'a2a87d4c-afd5-4803-ba87-9c64c92aad67'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @MaritalStatusPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @MaritalStatusPatientDetailColumnId, @AuditId, 'MaritalStatus', 'Marital Status',
    10, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'MaritalStatusDSC', SYSDATETIME(), @CreatedBy
  )
END

-- Admit Unit
declare @AdmitUnitPatientDetailColumnId uniqueidentifier = '7ea54f68-22ff-41ff-b757-a39fcb5d64e7'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @AdmitUnitPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AdmitUnitPatientDetailColumnId, @AuditId, 'AdmitUnit', 'Admit Unit',
    11, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'AdmitDepartmentNM', SYSDATETIME(), @CreatedBy
  )
END

-- Admit Date
declare @AdmitDatePatientDetailColumnId uniqueidentifier = 'a550a0e4-8966-49e3-8c96-a9142333ba12'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @AdmitDatePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AdmitDatePatientDetailColumnId, @AuditId, 'AdmitDate', 'Admit Date',
    12, 'Date', 'Survey', 'MortalityAuditWithEncounter',
    'AdmitDTS', SYSDATETIME(), @CreatedBy
  )
END

-- Admit Date
declare @InpatientAdmitDatePatientDetailColumnId uniqueidentifier = '8b20526e-e76b-46a9-89ef-69e776d7f654'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @InpatientAdmitDatePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @InpatientAdmitDatePatientDetailColumnId, @AuditId, 'InpatientAdmitDate', 'Inpatient Admit Date',
    13, 'Date', 'Survey', 'MortalityAuditWithEncounter',
    'InpatientAdmitDTS', SYSDATETIME(), @CreatedBy
  )
END

-- Deceased Date
declare @DeceasedDatePatientDetailColumnId uniqueidentifier = '1e9bef03-ad38-491f-b828-8db49398dbd4'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @DeceasedDatePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @DeceasedDatePatientDetailColumnId, @AuditId, 'DeceasedDate', 'Deceased Date',
    14, 'Date', 'Survey', 'MortalityAuditWithEncounter',
    'DeathDTS', SYSDATETIME(), @CreatedBy
  )
END

-- Deceased Unit
declare @DeceasedUnitPatientDetailColumnId uniqueidentifier = 'e2a35692-f968-48f5-beda-533dd634aa19'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @DeceasedUnitPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @DeceasedUnitPatientDetailColumnId, @AuditId, 'DeceasedUnit', 'Deceased Unit',
    15, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'Location', SYSDATETIME(), @CreatedBy
  )
END

-- Previous Discharge Date
declare @PreviousDischargeDatePatientDetailColumnId uniqueidentifier = 'c8d3c0d2-2d7f-4c44-925f-4c65ee19a6f1'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @PreviousDischargeDatePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @PreviousDischargeDatePatientDetailColumnId, @AuditId, 'PreviousDischargeDate', 'Previous Discharge Date',
    16, 'Date', 'Survey', 'MortalityAuditWithEncounter',
    'PreviousDischargeDTS', SYSDATETIME(), @CreatedBy
  )
END

-- Service
declare @ServicePatientDetailColumnId uniqueidentifier = 'ef648adf-734e-4750-a977-99461a4ab7f7'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @ServicePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @ServicePatientDetailColumnId, @AuditId, 'Service', 'Service',
    17, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'PrimaryServiceLineNM', SYSDATETIME(), @CreatedBy
  )
END

-- Attending LIP
declare @AttendingLIPPatientDetailColumnId uniqueidentifier = '80aa844d-a7b0-47f9-b45b-0c042b7dedae'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @AttendingLIPPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AttendingLIPPatientDetailColumnId, @AuditId, 'AttendingLip', 'Attending LIP',
    18, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'AttendingProviderFullNM', SYSDATETIME(), @CreatedBy
  )
END

-- Principal Diagnosis
declare @PrincipalDiagnosisPatientDetailColumnId uniqueidentifier = '3b5a1ca1-6bef-4856-801a-660ebf3d9b79'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @PrincipalDiagnosisPatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @PrincipalDiagnosisPatientDetailColumnId, @AuditId, 'PrincipalDiagnosis', 'Principal Diagnosis',
    19, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'PrimaryDiagnosisDSC', SYSDATETIME(), @CreatedBy
  )
END

-- Admit Source
declare @AdmitSourcePatientDetailColumnId uniqueidentifier = 'dc09afdb-24da-48c0-bf73-4b223bd055a4'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @AdmitSourcePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @AdmitSourcePatientDetailColumnId, @AuditId, 'AdmitSource', 'Admit Source',
    20, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'AdmitSourceDSC', SYSDATETIME(), @CreatedBy
  )
END

-- Principal Procedure
declare @PrincipalProcedurePatientDetailColumnId uniqueidentifier = 'aed0edbe-00b7-484d-8f76-456a575906ec'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @PrincipalProcedurePatientDetailColumnId))
BEGIN
  insert into [Survey].[AuditToolPatientDetail] (
    Id, AuditId, ColumnName, DisplayName,
    DisplayOrder, DataType, SchemaName, TableName,
    SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @PrincipalProcedurePatientDetailColumnId, @AuditId, 'PrincipalProcedure', 'Principal Procedure',
    21, 'Text', 'Survey', 'MortalityAuditWithEncounter',
    'PrimaryProcedureDSC', SYSDATETIME(), @CreatedBy
  )
END
