-- Form related IDs
DECLARE @FormId UNIQUEIDENTIFIER = '5E8F1C05-0DEB-4543-8D3A-A1ED31887E39'
DECLARE @OverviewSectionId UNIQUEIDENTIFIER = '56E55854-AC2C-429B-BF8D-0AD3F67C5836'
DECLARE @ReadmissionSectionId UNIQUEIDENTIFIER = '4A15B296-04E8-4345-A383-557C5721AF7E'
DECLARE @EventSectionId UNIQUEIDENTIFIER = '5A22D01E-8693-4525-A234-AA0FAFF821C8'
DECLARE @RecommendedSectionId UNIQUEIDENTIFIER = '14E7D417-55AE-4ACD-96CF-6FDA09A7D965'
DECLARE @CreatedBy VARCHAR(100) = 'Health Catalyst'
DECLARE @CreatedDate DATETIME = SYSDATETIME()

-- Question type IDs
DECLARE @MultipleChoiceQuestionTypeId INT = (Select TypeId from Survey.QuestionType where TypeNM = 'Multiple-Choice')
Declare @QuestionYesNoText int = (Select TypeId from Survey.QuestionType where TypeNM = 'Dichotomous-Text')
DECLARE @QuestionCheckBoxListOther INT = (Select TypeId from Survey.QuestionType where TypeNM = 'Check-box-list-other')
DECLARE @TextArea int = (Select TypeID from Survey.QuestionType where TypeNM = 'Text-Area')

DECLARE @Question3Id UNIQUEIDENTIFIER = '1F72CE0E-537A-40CF-9CB5-5A0BCA873AFC'

IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question3Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question3Id
    ,@FormId
    ,@OverviewSectionId
    ,3 --order
    ,'What was the condition on admission?'
    ,@MultipleChoiceQuestionTypeId
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionOption] (
    [QuestionOptionID]
    ,[QuestionID]
    ,[OptionOrderNBR]
    ,[OptionTXT]
    ,[IsActiveFLG]
    ,[DependentQuestionOptionID]
    ,[IsDefaultOption])
  VALUES (
    'E8228ADF-98F7-4AAC-A106-BFA74B4FE45B'
    ,@Question3Id
    ,1
    ,'Elective'
    ,1
    ,NULL
    ,0),

    (
    '5DCCE2DF-6B80-4F42-8E0B-0C9D644EDDB6'
    ,@Question3Id
    ,2
    ,'Emergent'
    ,1
    ,NULL
    ,0),

    (
    '276E5113-6D79-49AF-92E8-CF785928A46A'
    ,@Question3Id
    ,3
    ,'Urgent'
    ,1
    ,NULL
    ,0)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '806F91B8-935D-4F4E-BBA7-934943D97182'
    ,@Question3Id
    ,'Registration Admit Type'
    ,'AdmitType'
    ,1
    ,'String'
    ,1),

    (
    '770D9E33-8FE3-45D1-9285-77454CE029DE'
    ,@Question3Id
    ,'Admit Date/Time'
    ,'HospitalAdmitDts'
    ,2
    ,'Date/Time'
    ,1)
END

DECLARE @Question5Id UNIQUEIDENTIFIER = 'D175FF12-5169-4974-8120-95275B5C3941'

IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question5Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question5Id
    ,@FormId
    ,@OverviewSectionId
    ,5 --order
    ,'Was the patient DNI/DNR on admission?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    'F2653BD3-FEAE-4025-9065-78ACD8851D21'
    ,@Question5Id
    ,'Code Status Order'
    ,'CprOrderDsc'
    ,1
    ,'String'
    ,1),

    (
    'C4787CCB-7665-408D-BF1C-EDCCA05C1CA8'
    ,@Question5Id
    ,'Code Status Order Date/Time'
    ,'CprOrderDts'
    ,2
    ,'Date/Time'
    ,1)
END

DECLARE @Question6Id UNIQUEIDENTIFIER = '04179958-B2A2-4DA1-99F8-ABCBC19CE55B'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question6Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question6Id
    ,@FormId
    ,@OverviewSectionId
    ,6 --order
    ,'Was patient made DNI/DNR during this hospitalization?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '1F02288C-2839-4460-87A0-E4B80F2E82F4'
    ,@Question6Id
    ,'Code Status Order'
    ,'CprOrderDsc'
    ,1
    ,'String'
    ,1),

    (
    'A15FF9C0-701A-44B9-A853-F08FAE16A942'
    ,@Question6Id
    ,'Code Status Order Date/Time'
    ,'CprOrderDts'
    ,2
    ,'Date/Time'
    ,1)

  INSERT INTO [Survey].[DecisionValue] (
    [DecisionValueID]
    ,[SourceQuestionID]
    ,[EnableQuestionID]
    ,[DecisionResponse]
    ,[DecisionResponseID]
    ,[AllOptionsEnableFlg]
    ,[InvertEnable])
  VALUES (
    'C260E69B-DBAA-419E-8E3C-E19BCEF687B8'
    ,@Question5Id
    ,@Question6Id
    ,'No'
    ,NULL
    ,0
    ,0)
END

DECLARE @Question7Id UNIQUEIDENTIFIER = 'F84BBD41-E4A5-4FD1-AA4A-B1B1D8AF5664'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question7Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question7Id
    ,@FormId
    ,@OverviewSectionId
    ,7 --order
    ,'Was the patient Comfort Care Only?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    'CA558301-67EC-4911-B678-62413E48A52F'
    ,@Question7Id
    ,'Comfort Care Order'
    ,'ComfortMeasureOrderDsc'
    ,1
    ,'String'
    ,1),

    (
    '57E54D51-D946-4BC3-8CB2-5C3985CF43F3'
    ,@Question7Id
    ,'Comfort Care Order Date/Time'
    ,'ComfortMeasureOrderDts'
    ,2
    ,'Date/Time'
    ,1)
END

DECLARE @Question8Id UNIQUEIDENTIFIER = '3E989392-F01D-4EFA-92B1-694CF09BF79C'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question8Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question8Id
    ,@FormId
    ,@OverviewSectionId
    ,8 --order
    ,'Was the patient Comfort Care within 48 hours of admit?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '8CDEAD9F-05D1-4BCF-BF72-6632947C5983'
    ,@Question8Id
    ,'Admit Date/Time'
    ,'HospitalAdmitDts'
    ,1
    ,'Date/Time'
    ,1),

    (
    'FC420593-1FB2-40DD-93E3-6B54F87F755E'
    ,@Question8Id
    ,'Comfort Care Order Date/Time'
    ,'ComfortMeasureOrderDts'
    ,2
    ,'Date/Time'
    ,1),

    (
    'EA60C272-835B-4C09-97A3-6F9E8422F486'
    ,@Question8Id
    ,'Comfort Care Admit to Order Duration'
    ,'AdmitToComfortCareMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)

  INSERT INTO [Survey].[DecisionValue] (
    [DecisionValueID]
    ,[SourceQuestionID]
    ,[EnableQuestionID]
    ,[DecisionResponse]
    ,[DecisionResponseID]
    ,[AllOptionsEnableFlg]
    ,[InvertEnable])
  VALUES (
    '8CA2A481-DDE3-4D44-B1DB-3B5D083B9042'
    ,@Question7Id
    ,@Question8Id
    ,'Yes'
    ,NULL
    ,0
    ,0)
END

DECLARE @Question9Id UNIQUEIDENTIFIER = 'CBFB51F5-9169-4633-996A-F9B930ADE50D'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question9Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question9Id
    ,@FormId
    ,@ReadmissionSectionId
    ,9 --order
    ,'Readmission within 30 days?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '18C5AD3C-A655-4088-894B-1E0B8C68E713'
    ,@Question9Id
    ,'Previous discharge date'
    ,'PreviousDischargeDts'
    ,1
    ,'Date/Time'
    ,1),

    (
    '092B6B1A-82AE-465F-8657-6F3FE457B8E7'
    ,@Question9Id
    ,'Admit Date/Time'
    ,'HospitalAdmitDts'
    ,2
    ,'Date/Time'
    ,1),

    (
    '75FD5204-ED70-4486-8D66-7E8F2B62C375'
    ,@Question9Id
    ,'Readmission Duration'
    ,'ReadmissionMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

DECLARE @Question10Id UNIQUEIDENTIFIER = 'F655EBBE-20B5-4269-8B57-BFB9982DEB10'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question10Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question10Id
    ,@FormId
    ,@ReadmissionSectionId
    ,10 --order
    ,'Was the patient seen in the Emergency Department within 3 days of this visit?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    'D8E584CF-577D-4B7F-84BE-225356726FAE'
    ,@Question10Id
    ,'Previous ED visit date'
    ,'PreviousEDDischargeDTS'
    ,1
    ,'Date/Time'
    ,1),

    (
    '837AD7C4-815A-434B-A592-338AD3EC0224'
    ,@Question10Id
    ,'Admit Date/Time'
    ,'HospitalAdmitDts'
    ,2
    ,'Date/Time'
    ,1),

    (
    '3B9E7616-BE40-42FE-B98D-676ABA3C06A2'
    ,@Question10Id
    ,'ED to Admit Duration'
    ,'EDReadmissionMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

--Q1
DECLARE @Question10aId UNIQUEIDENTIFIER = '51adf155-046a-48e2-8d6d-5627bea70893'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question10aId))
  BEGIN
    INSERT INTO [Survey].[Question] (
                                      [QuestionID]
                                    ,[FormID]
                                    ,[SectionID]
                                    ,[DisplayOrderNBR]
                                    ,[QuestionTXT]
                                    ,[QuestionTypeID]
                                    ,[QuestionMultiSelectTypeID]
                                    ,[CreatedDTS]
                                    ,[CreatedByTXT]
                                    ,[IsActiveFLG]
                                    ,[IsRequired]
                                    ,[ParentQuestionId]
                                    ,[ParentOptionId]
                                    ,[HelpText]
                                    ,[QuestionSubTXT])
    VALUES (
             @Question10aId
           ,@FormId
           ,@ReadmissionSectionId
           ,10 --order
           ,'Total number of INTEGRIS Healthcare provider encounters in 6 months prior to mortality admission.'
           ,@TextArea
           ,null
           ,@CreatedDate
           ,@CreatedBy
           ,1
           ,0
           ,null
           ,null
           ,null
           ,null)

    INSERT INTO [Survey].[QuestionHelpData] (
                                              [Id]
                                            ,[QuestionID]
                                            ,[Description]
                                            ,[ColumnName]
                                            ,[DisplayOrder]
                                            ,[DataType]
                                            ,[ShowInExport])
    VALUES (
            'd7b9bb14-aeb1-4f5b-8d1b-3b961880e0de'
            , @Question10aId
            ,'Total Readmissions prior 6 months'
            ,'ReadmissionTotalNBR'
            ,1
            ,'String'
            ,1)


  END

DECLARE @Question11Id UNIQUEIDENTIFIER = 'FF9C0674-C73E-4901-BECF-B53E5A5F75BF'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question11Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question11Id
    ,@FormId
    ,@EventSectionId
    ,11 --order
    ,'Was the patient death within 24 hours of admission?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '2F52E060-CB85-4D67-849B-56A8BFD681BF'
    ,@Question11Id
    ,'Admit Date/Time'
    ,'HospitalAdmitDts'
    ,1
    ,'Date/Time'
    ,1),

    (
    '38289823-416F-4A9D-9D60-88B6D9D90FDC'
    ,@Question11Id
    ,'Deceased Date'
    ,'DeathDate'
    ,2
    ,'Date/Time'
    ,1),

    (
    '211C14F0-8BAD-4F0A-9BA3-9185BFBF9765'
    ,@Question11Id
    ,'Admit to death duration'
    ,'AdmitToDeathMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

DECLARE @Question12Id UNIQUEIDENTIFIER = '3AB52385-BD67-48FE-8952-DAE05AF0B457'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question12Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question12Id
    ,@FormId
    ,@EventSectionId
    ,12 --order
    ,'Was the death within 48 hours of surgery/procedure?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    'AE4D016F-DC27-4292-B168-395925E23972'
    ,@Question12Id
    ,'Procedure date'
    ,'RecentSurgicalProcedureDate'
    ,1
    ,'Date/Time'
    ,1),

    (
    '2992B238-5CF5-477C-8422-8B6481497283'
    ,@Question12Id
    ,'Deceased Date'
    ,'DeathDate'
    ,2
    ,'Date/Time'
    ,1),

    (
    'B098E98E-4DC7-4658-AFA5-F845BECD4012'
    ,@Question12Id
    ,'Procedure to death duration'
    ,'SurgeryToDeathMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

DECLARE @Question13Id UNIQUEIDENTIFIER = '174D2631-DB23-4A6B-8091-AB59B763B3D0'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question13Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question13Id
    ,@FormId
    ,@EventSectionId
    ,13 --order
    ,'Was the admit decision to ED departure time greater than 6 hours?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '093F84AE-40D4-4CA2-97D9-C17EE802F08B'
    ,@Question13Id
    ,'Admit Order Date/Time'
    ,'AdmitOrderDTS'
    ,1
    ,'Date/Time'
    ,1),

    (
    'D614B313-CDC9-4127-96B1-C7DD387CEE29'
    ,@Question13Id
    ,'Admit Date/Time'
    ,'HospitalAdmitDts'
    ,2
    ,'Date/Time'
    ,1),

    (
    '80A048BA-8B95-4F57-B12C-20B72DCA1DD2'
    ,@Question13Id
    ,'Admit order to admit duration'
    ,'AdmitOrderToAdmitMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

DECLARE @Question14Id UNIQUEIDENTIFIER = '4EE164D4-029C-4769-AA22-6390E93C90BA'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question14Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question14Id
    ,@FormId
    ,@EventSectionId
    ,14 --order
    ,'Transfer from floor to ICU within 24 hours of admission?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '00FE9597-1A5E-46EA-A855-4087F3A71C72'
    ,@Question14Id
    ,'Admit Date/Time'
    ,'HospitalAdmitDts'
    ,1
    ,'Date/Time'
    ,1),

    (
    'C688D9DF-49C1-4AFF-9577-9887F3616266'
    ,@Question14Id
    ,'Transfer to ICU Date/Time'
    ,'ICUAdmitDTS'
    ,2
    ,'Date/Time'
    ,1),

    (
    '0F3F0867-35ED-485A-B49D-B94FE55FACB7'
    ,@Question14Id
    ,'Floor to ICU Duration'
    ,'AdmitToICUMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

DECLARE @Question15Id UNIQUEIDENTIFIER = '4D872410-08A1-412E-8DBA-B0B35C550BAB'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question15Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question15Id
    ,@FormId
    ,@EventSectionId
    ,15 --order
    ,'Did the patient return to ICU within 48 hours after transfer out of ICU?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '88EFA31F-C5FA-4D01-9993-A0CC3AB10A0D'
    ,@Question15Id
    ,'ICU transfer out date'
    ,'ICUDischargeDTS'
    ,1
    ,'Date/Time'
    ,1),

    (
    '59F0C314-91D0-4D35-90C2-AD144443586F'
    ,@Question15Id
    ,'ICU transfer in date'
    ,'ICUReadmitDTS'
    ,2
    ,'Date/Time'
    ,1),

    (
    'F6134847-A409-4E33-A7B9-B420609638B8'
    ,@Question15Id
    ,'ICU return duration'
    ,'ICUReadmissionMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

DECLARE @Question20Id UNIQUEIDENTIFIER = 'F3F74003-79F6-45DE-AB80-20E1EB3F08E7'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question20Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question20Id
    ,@FormId
    ,@EventSectionId
    ,20 --order
    ,'Did the patient fall during this hospitalization?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '0AB800F7-A61A-476D-8A02-9C5012B8AF7E'
    ,@Question20Id
    ,'Fall Date/Time'
    ,'FallDTS'
    ,1
    ,'Date/Time'
    ,1)
END

DECLARE @Question21Id UNIQUEIDENTIFIER = '8330A154-4FB4-4A9E-AFD0-1B68365DE6CE'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question21Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question21Id
    ,@FormId
    ,@EventSectionId
    ,21 --order
    ,'Were restraints used within 24 hours of death?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    'E4EA6EC9-BA1E-40B8-A2B0-786F58C8FCC1'
    ,@Question21Id
    ,'Restraint Order Date/Time'
    ,'RestraintOrderDTS'
    ,1
    ,'Date/Time'
    ,1),

    (
    '109B2E6A-DFBC-4E7C-AAE5-E1BB9E7B408F'
    ,@Question21Id
    ,'Death Date/Time'
    ,'DeathDate'
    ,2
    ,'Date/Time'
    ,0),

    (
    '967FDA7D-46B3-40DE-A910-38CDDD64E678'
    ,@Question21Id
    ,'Restraint to Death Duration'
    ,'RestraintToDeathMinuteNBR'
    ,3
    ,'Duration/Hour'
    ,1)
END

-- Stopping this from displaying in export because the death date already displays in the Encounter Information section
UPDATE Survey.[QuestionHelpData]
SET ShowInExport = 0
WHERE ID = '109B2E6A-DFBC-4E7C-AAE5-E1BB9E7B408F'


DECLARE @Question23Id UNIQUEIDENTIFIER = '71BB1A21-696A-4FA9-B611-1B9C726F8543'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question23Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question23Id
    ,@FormId
    ,@EventSectionId
    ,23 --order
    ,'Was there a change in procedure?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '11B86D2B-F8D1-4C27-9225-E65223605258'
    ,@Question23Id
    ,'Scheduled Procedure'
    ,'ScheduledSurgicalProcedure'
    ,1
    ,'String'
    ,1),

    (
    'F41E70C7-7856-4B28-9492-AB79BAD75977'
    ,@Question23Id
    ,'Final Procedure'
    ,'RecentSurgicalProcedure'
    ,2
    ,'Date/Time'
    ,1)
END

DECLARE @Question26Id UNIQUEIDENTIFIER = '6D0727F5-3CCC-44CA-B1D0-BCA2E82644DD'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question26Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question26Id
    ,@FormId
    ,@EventSectionId
    ,26 --order
    ,'Was the patient intubated (non-surgical)?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '09A2666C-934F-4D73-83E2-4B5CB3F5611E'
    ,@Question26Id
    ,'Initial Intubation Date/Time'
    ,'IntubationDts'
    ,1
    ,'Date/Time'
    ,1)
END

DECLARE @Question27Id UNIQUEIDENTIFIER = 'A6A7D96A-900A-4483-9540-B9428C08D867'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question27Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question27Id
    ,@FormId
    ,@EventSectionId
    ,27 --order
    ,'Was the patient reintubated?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    'CEC8482B-BF8D-47B5-B176-BE11A8473CAF'
    ,@Question27Id
    ,'Reintubation Date/Time'
    ,'ReIntubationDts'
    ,1
    ,'Date/Time'
    ,1)
END

DECLARE @Question28Id UNIQUEIDENTIFIER = '7ce0709c-8ae6-40e3-b28b-b8c5eb7c4e03'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question28Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question28Id
    ,@FormId
    ,@EventSectionId
    ,28 --order
    ,'Were there diagnostic studies for emboli or DVT?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '438bc883-f02b-44bc-a3b4-b575fb7d46fe'
    ,@Question28Id
    ,'Study'
    ,'DiagnosticStudyTxt'
    ,1
    ,'Array'
    ,1)
END

DECLARE @Question29Id UNIQUEIDENTIFIER = '9e4e35f8-799a-4f61-a4f6-a42ce38c1745'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @Question29Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @Question29Id
    ,@FormId
    ,@EventSectionId
    ,29 --order
    ,'Was there a PTT >100 and/or INR >6 within 48 hours of death?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
    [Id]
    ,[QuestionID]
    ,[Description]
    ,[ColumnName]
    ,[DisplayOrder]
    ,[DataType]
    ,[ShowInExport])
  VALUES (
    '31e69f53-929f-46aa-a16c-1c80db00731f'
    ,@Question29Id
    ,'PTT/INR'
    ,'PttInrTxt'
    ,1
    ,'Array'
    ,1)
END

DECLARE @NewQuestion7Id UNIQUEIDENTIFIER = 'a25d855b-830b-4898-a057-c60afc97efbc'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @NewQuestion7Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @NewQuestion7Id
    ,@FormId
    ,@OverviewSectionId
    ,7 --order
    ,'What co-morbidities existed for the patient?'
    ,@QuestionCheckBoxListOther
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

    INSERT INTO Survey.QuestionOption (
      QuestionOptionID
      ,QuestionID
      ,OptionOrderNBR
      ,OptionTXT
      ,IsActiveFLG
      ,IsDefaultOption
    ) VALUES
    ('213742b1-7054-4571-b547-1bbb1f4632ba', @NewQuestion7Id, 1, 'Asthma', 1, 0)
    ,('41669bd7-0c0a-46ea-bb2f-2ad8ad5e44dd', @NewQuestion7Id, 2, 'Cancer', 1, 0)
    ,('9850c380-c0d7-48cd-9cac-e069c2f8ccef', @NewQuestion7Id, 3, 'Chronic Kidney Disease', 1, 0)
    ,('e9211a63-9c4c-4994-abbd-7ae63fe92bcc', @NewQuestion7Id, 4, 'COPD', 1, 0)
    ,('84ee01a2-5132-4417-a919-688262f00e9e', @NewQuestion7Id, 5, 'CVA', 1, 0)
    ,('c5b327df-5d32-480e-b002-95a79845d878', @NewQuestion7Id, 6, 'Diabetes', 1, 0)
    ,('306b0f3b-b1b8-465c-a00c-63710678abb1', @NewQuestion7Id, 7, 'Embolism or Thrombosis', 1, 0)
    ,('c224d86c-88ce-49af-8ac5-d9f44b20de80', @NewQuestion7Id, 8, 'End Stage Liver Disease', 1, 0)
    ,('ca1a00a6-4ce2-4383-9866-f7e0dd873d0f', @NewQuestion7Id, 9, 'End Stage Renal Disease', 1, 0)
    ,('ce1b6865-8d85-4c00-8a8c-7528b34fa1d8', @NewQuestion7Id, 10, 'GI Conditions/Disease', 1, 0)
    ,('e117c7bf-b815-47d8-8837-c3f5ccabd5f3', @NewQuestion7Id, 11, 'Heart Disease', 1, 0)
    ,('7ca575bb-14b7-4ae5-8031-cd7161f178ae', @NewQuestion7Id, 12, 'Heart Failure', 1, 0)
    ,('6dd60ae1-b0c3-435b-a829-5dc820918457', @NewQuestion7Id, 13, 'Hypertension', 1, 0)
    ,('cb457ae4-ab0c-4616-9cfe-85aca5bb1c7a', @NewQuestion7Id, 14, 'Infection', 1, 0)
    ,('153aa5c6-c660-4714-b83c-19aa80e6d3f1', @NewQuestion7Id, 15, 'Inflammatory Bowel Disease', 1, 0)
    ,('05243d0a-8fd9-4a71-bbe7-0a1c5e1d3995', @NewQuestion7Id, 16, 'Major Organ Transplant', 1, 0)
    ,('2a916de7-10cf-4eb2-aeee-30f2a4e98c47', @NewQuestion7Id, 17, 'Mental Health Concerns', 1, 0)
    ,('84ad0924-7f4c-4a9f-9ce5-8f3590950333', @NewQuestion7Id, 18, 'Myoneural Junction and Muscle Disease', 1, 0)
    ,('120cc896-dd43-44de-9b50-c03b2b21509a', @NewQuestion7Id, 19, 'Neorologic Deficits', 1, 0)
    ,('cb2bcf96-7795-4319-9ed6-8415ad51172a', @NewQuestion7Id, 20, 'Obesity', 1, 0)
    ,('85f958b8-de2e-44d0-95a0-6c160815848f', @NewQuestion7Id, 21, 'Osteomyelitis', 1, 0)
    ,('94f6921e-f1c8-448d-8ec0-d751cf7ad994', @NewQuestion7Id, 22, 'Pancreatitis', 1, 0)
    ,('ac0d4a3f-f4e3-4a89-a99c-a2428fa48813', @NewQuestion7Id, 23, 'Pregnacy/Postpartum Hypertension', 1, 0)
    ,('5d33abac-6b62-4920-8446-5930df9de15e', @NewQuestion7Id, 24, 'Rheumatoid and Other Inflammatory Arthropathies', 1, 0)
    ,('634a3e4d-6e57-461f-aacf-00914d6b0069', @NewQuestion7Id, 25, 'Seizure', 1, 0)
    ,('78adf83d-116f-417d-b325-b82826812e75', @NewQuestion7Id, 26, 'Vascular Disease', 1, 0)

  INSERT INTO [Survey].[QuestionHelpData] (
          [Id]
          ,[QuestionID]
          ,[Description]
          ,[ColumnName]
          ,[DisplayOrder]
          ,[DataType]
          ,[ShowInExport]
          ,[ShowCount]
          ,[ExportColumnName]
  )
  VALUES (
          '0f736bc2-49f6-4db7-91b9-bd68dbd0bf38'
          ,@NewQuestion7Id
          ,'Total emergency room; outpatient; or observation visits in the last 6 months'
          ,'ChronicConditionTxt'
          ,1
          ,'Array'
          ,1
          ,1
          ,'Encounter.MortalityAudit.ChronicConditionTxt'
  )
END

DECLARE @NewQuestion8Id UNIQUEIDENTIFIER = 'fbe2fb9b-c749-48e9-b3b1-329e7db75c0f'
IF (NOT EXISTS(SELECT * FROM Survey.[Question] where QuestionID = @NewQuestion8Id))
BEGIN
  INSERT INTO [Survey].[Question] (
    [QuestionID]
    ,[FormID]
    ,[SectionID]
    ,[DisplayOrderNBR]
    ,[QuestionTXT]
    ,[QuestionTypeID]
    ,[QuestionMultiSelectTypeID]
    ,[CreatedDTS]
    ,[CreatedByTXT]
    ,[IsActiveFLG]
    ,[IsRequired]
    ,[ParentQuestionId]
    ,[ParentOptionId]
    ,[HelpText]
    ,[QuestionSubTXT])
  VALUES (
    @NewQuestion8Id
    ,@FormId
    ,@OverviewSectionId
    ,8 --order
    ,'Was an Infectious Disease Consult ordered during this visit?'
    ,@QuestionYesNoText
    ,null
    ,@CreatedDate
    ,@CreatedBy
    ,1
    ,0
    ,null
    ,null
    ,null
    ,null)

  INSERT INTO [Survey].[QuestionHelpData] (
          [Id]
          ,[QuestionID]
          ,[Description]
          ,[ColumnName]
          ,[DisplayOrder]
          ,[DataType]
          ,[ShowInExport]
          ,[ExportColumnName]
  )
  VALUES (
    'efcda9c1-6b7a-4353-a7a6-f836abd296df'
    ,@NewQuestion8Id
    ,'Order'
    ,'DiseaseConsult'
    ,1
    ,'String'
    ,1
    ,'Encounter.MortalityAudit.DiseaseConsult'
  ),
  (
    '18e3ac7b-7975-43a2-b423-cf561b868eeb'
    ,@NewQuestion8Id
    ,'Order date/time'
    ,'DiseaseConsultDts'
    ,2
    ,'Date/Time'
    ,1
    ,'Encounter.MortalityAudit.DiseaseConsultDts'
  )
END

GO

Update [Survey].[QuestionHelpData] SET DataType = 'String' where ID = 'F41E70C7-7856-4B28-9492-AB79BAD75977' and DataType='Date/Time'
GO
