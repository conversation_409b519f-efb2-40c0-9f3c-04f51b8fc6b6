namespace SafetySurveillance.WebAPI.UnitTests.Repositories
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using MockQueryable.Moq;
    using Moq;
    using SafetySurveillanceWebApi.Data;
    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Repositories;

    [TestClass]
    public class PatientRepositoryTests
    {
        private PatientRepository patientRepository;

        [TestInitialize]
        public void SetUp()
        {
        }

        [TestClass]
        public class GetPatientAllergiesAsync : PatientRepositoryTests
        {
            [ExpectedException(typeof(ArgumentNullException))]
            [TestMethod]
            public async Task ShouldThrowExceptionWhenPatientIdNull()
            {
                // Arrange
                var data = new List<PatientAllergy>
                               {
                                   new PatientAllergy() { PatientId = "1234" },
                                   new PatientAllergy() { PatientId = "4567" },
                                   new PatientAllergy() { PatientId = "1234" }
                               }.AsQueryable().BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientAllergies).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientAllergiesAsync(null);

                // Assert
                // expect exception
            }

            [TestMethod]
            public async Task ShouldReturnListOfPatientAllergies()
            {
                // Arrange
                var data = new List<PatientAllergy>
                               {
                                   new PatientAllergy() { PatientId = "1234" },
                                   new PatientAllergy() { PatientId = "4567" },
                                   new PatientAllergy() { PatientId = "1234" }
                               }.AsQueryable().BuildMockDbSet();

                var mockPatientId = "1234";

                var comparator = new List<PatientAllergy>
                                     {
                                         new PatientAllergy() { PatientId = "1234" },
                                         new PatientAllergy() { PatientId = "1234" }
                                     };

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientAllergies).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientAllergiesAsync(mockPatientId);

                // Assert
                for (var i = 0; i < result.Count; i++)
                {
                    Assert.AreEqual(comparator[i].PatientId, result[i].PatientId);
                }

                Assert.AreEqual(comparator.Count, result.Count);
            }
        }

        [TestClass]
        public class GetPatientMedicationOrdersAsync : PatientRepositoryTests
        {
            [ExpectedException(typeof(ArgumentNullException))]
            [TestMethod]
            public async Task ShouldReturnExceptionIfEncountersNull()
            {
                // Arrange
                var data = new List<PatientActiveMedicationOrder>
                               {
                                   new PatientActiveMedicationOrder() { PatientEncounterId = "1234" },
                                   new PatientActiveMedicationOrder() { PatientEncounterId = "4567" },
                                   new PatientActiveMedicationOrder() { PatientEncounterId = "78910" }
                               }
                                .AsQueryable()
                                .BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientMedicationOrders).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientMedicationNamesAsync(null);

                // Assert
                // expect exception
            }

            [TestMethod]
            public async Task ShouldReturnListOfPatientActiveMedicationOrders()
            {
                // Arrange
                var data = new List<PatientActiveMedicationOrder>
                               {
                                   new PatientActiveMedicationOrder() { PatientEncounterId = "1234" },
                                   new PatientActiveMedicationOrder() { PatientEncounterId = "4567" },
                                   new PatientActiveMedicationOrder() { PatientEncounterId = "78910" }
                               }
                                .AsQueryable()
                                .BuildMockDbSet();

                var mockEncounters = new List<TriggerDetail>
                                         {
                                             new TriggerDetail() { PatientEncounterId = "1234" },
                                             new TriggerDetail() { PatientEncounterId = "4567" }
                                         };

                var comparator = new List<PatientActiveMedicationOrder>
                                     {
                                         new PatientActiveMedicationOrder() { PatientEncounterId = "1234" },
                                         new PatientActiveMedicationOrder() { PatientEncounterId = "4567" }
                                     };

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientMedicationOrders).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientMedicationOrdersAsync(mockEncounters);

                // Assert
                for (var i = 0; i < result.Count; i++)
                {
                    Assert.AreEqual(comparator[i].PatientEncounterId, result[i].PatientEncounterId);
                }

                Assert.AreEqual(comparator.Count, result.Count);
            }
        }

        [TestClass]
        public class GetPatientSurgeryProceduresAsync : PatientRepositoryTests
        {
            [ExpectedException(typeof(ArgumentNullException))]
            [TestMethod]
            public async Task ShouldThrowExceptionIfEncountersNull()
            { 
                // Arrange
                var data = new List<PatientSurgeryProcedures>
                                   {
                                       new PatientSurgeryProcedures() { PatientEncounterId = "1234" },
                                       new PatientSurgeryProcedures() { PatientEncounterId = "4567" },
                                       new PatientSurgeryProcedures() { PatientEncounterId = "78910" }
                                   }
                                .AsQueryable()
                                .BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientSurgeryProcedures).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientSurgeryProceduresAsync(null);

                // Assert
                // expect exception
            }

            [TestMethod]
            public async Task ShouldReturnListOfPatientSurgeryProcedures()
            {
                // Arrange
                var data = new List<PatientSurgeryProcedures>
                                   {
                                       new PatientSurgeryProcedures() { PatientEncounterId = "1234" },
                                       new PatientSurgeryProcedures() { PatientEncounterId = "4567" },
                                       new PatientSurgeryProcedures() { PatientEncounterId = "78910" }
                                   }
                                .AsQueryable()
                                .BuildMockDbSet();

                var mockEncounters = new List<TriggerDetail>
                                             {
                                                 new TriggerDetail() { PatientEncounterId = "1234" },
                                                 new TriggerDetail() { PatientEncounterId = "4567" }
                                             };

                var comparator = new List<PatientSurgeryProcedures>
                                         {
                                             new PatientSurgeryProcedures() { PatientEncounterId = "1234" },
                                             new PatientSurgeryProcedures() { PatientEncounterId = "4567" }
                                         };

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientSurgeryProcedures).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientSurgeryProceduresAsync(mockEncounters);

                // Assert
                for (var i = 0; i < result.Count; i++)
                {
                    Assert.AreEqual(comparator[i].PatientEncounterId, result[i].PatientEncounterId);
                }

                Assert.AreEqual(comparator.Count, result.Count);
            }
        }

        [TestClass]
        public class GetPatientLabsAsync : PatientRepositoryTests
        {
            [ExpectedException(typeof(ArgumentNullException))]
            [TestMethod]
            public async Task ShouldThrowExceptionIfEncountersNull()
            {
                // Arrange
                var data = new List<PatientLab>
                                   {
                                       new PatientLab() { PatientEncounterId = "1234" },
                                       new PatientLab() { PatientEncounterId = "4567" },
                                       new PatientLab() { PatientEncounterId = "78910" }
                                   }
                                .AsQueryable()
                                .BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientLabs).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientSurgeryProceduresAsync(null);

                // Assert
                // expect exception
            }

            [TestMethod]
            public async Task ShouldReturnListOfPatientLabsWhenNoLabNamesSent()
            {
                // Arrange
                var data = new List<PatientLab>
                                   {
                                       new PatientLab() { PatientEncounterId = "1234", LabNm = "Lab1"},
                                       new PatientLab() { PatientEncounterId = "4567", LabNm = "Lab2"},
                                       new PatientLab() { PatientEncounterId = "4567", LabNm = "Lab3"},
                                       new PatientLab() { PatientEncounterId = "78910", LabNm = "Lab3"}
                                   }
                                .AsQueryable()
                                .BuildMockDbSet();

                var mockEncounters = new List<TriggerDetail>
                                             {
                                                 new TriggerDetail() { PatientEncounterId = "1234" },
                                                 new TriggerDetail() { PatientEncounterId = "4567" },
                                                 new TriggerDetail() { PatientEncounterId = "78910" }
                                             };

                var mockLabNames = new List<string>
                                            {
                                            };

                var mockLabFilter = new PatientLabNameFilter();
                mockLabFilter.Encounters = mockEncounters;
                mockLabFilter.LabNames = mockLabNames;

                var comparator = new List<PatientLab>
                                         {
                                             new PatientLab() { PatientEncounterId = "1234", LabNm = "Lab1"},
                                             new PatientLab() { PatientEncounterId = "4567", LabNm = "Lab2"},
                                             new PatientLab() { PatientEncounterId = "4567", LabNm = "Lab3"},
                                             new PatientLab() { PatientEncounterId = "78910", LabNm = "Lab3"}
                                         };

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientLabs).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientLabsAsync(mockLabFilter);

                // Assert
                for (var i = 0; i < result.Count; i++)
                {
                    Assert.AreEqual(comparator[i].PatientEncounterId, result[i].PatientEncounterId);
                }

                Assert.AreEqual(comparator.Count, result.Count);
            }

            [TestMethod]
            public async Task ShouldReturnListOfFilteredPatientLabsWhenLabNamesSent()
            {
                // Arrange
                var data = new List<PatientLab>
                                   {
                                       new PatientLab() { PatientEncounterId = "1234", LabNm = "Lab1"},
                                       new PatientLab() { PatientEncounterId = "4567", LabNm = "Lab2"},
                                       new PatientLab() { PatientEncounterId = "4567", LabNm = "Lab3"},
                                       new PatientLab() { PatientEncounterId = "78910", LabNm = "Lab3"}
                                   }
                                .AsQueryable()
                                .BuildMockDbSet();

                var mockEncounters = new List<TriggerDetail>
                                             {
                                                 new TriggerDetail() { PatientEncounterId = "1234" },
                                                 new TriggerDetail() { PatientEncounterId = "4567" },
                                                 new TriggerDetail() { PatientEncounterId = "78910" }
                                             };

                var mockLabNames = new List<string>
                                            {
                                                "Lab1",
                                                "Lab2"
                                            };

                var mockLabFilter = new PatientLabNameFilter();
                mockLabFilter.Encounters = mockEncounters;
                mockLabFilter.LabNames = mockLabNames;

                var comparator = new List<PatientLab>
                                         {
                                             new PatientLab() { PatientEncounterId = "1234", LabNm = "Lab1"},
                                             new PatientLab() { PatientEncounterId = "4567", LabNm = "Lab2"}
                                         };

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientLabs).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientLabsAsync(mockLabFilter);

                // Assert
                for (var i = 0; i < result.Count; i++)
                {
                    Assert.AreEqual(comparator[i].PatientEncounterId, result[i].PatientEncounterId);
                }

                Assert.AreEqual(comparator.Count, result.Count);
            }
        }

        [TestClass]
        public class GetPatientRadiologyReportsAsync : PatientRepositoryTests
        {
            [ExpectedException(typeof(ArgumentNullException))]
            [TestMethod]
            public async Task ShouldThrowExceptionIfEncountersNull()
            {
                // Arrange
                var data = new List<PatientRadiologyOrder>
                    {
                        new PatientRadiologyOrder() {PatientEncounterId = "1234"},
                        new PatientRadiologyOrder() {PatientEncounterId = "4567"},
                        new PatientRadiologyOrder() {PatientEncounterId = "78910"}
                    }
                    .AsQueryable()
                    .BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientRadiologyOrders).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientSurgeryProceduresAsync(null);

                // Assert
                // expect exception
            }

            [TestMethod]
            public async Task ShouldReturnListOfPatientPatientRadiologyOrders()
            {
                // Arrange
                var data = new List<PatientRadiologyOrder>
                    {
                        new PatientRadiologyOrder() {PatientEncounterId = "1234", ProcedureNm = "Radiology1" },
                        new PatientRadiologyOrder() {PatientEncounterId = "4567", ProcedureNm  = "Radiology2" },
                        new PatientRadiologyOrder() {PatientEncounterId = "4567", ProcedureNm = "Radiology3" },
                        new PatientRadiologyOrder() {PatientEncounterId = "78910", ProcedureNm = "Radiology3" }
                    }
                    .AsQueryable()
                    .BuildMockDbSet();

                var mockEncounters = new List<TriggerDetail>
                {
                    new TriggerDetail() {PatientEncounterId = "1234" },
                    new TriggerDetail() {PatientEncounterId = "4567" }
                };

                var comparator = new List<PatientRadiologyOrder>
                {
                    new PatientRadiologyOrder() {PatientEncounterId = "1234", ProcedureNm = "Radiology1"},
                    new PatientRadiologyOrder() {PatientEncounterId = "4567", ProcedureNm = "Radiology2"},
                    new PatientRadiologyOrder() {PatientEncounterId = "4567", ProcedureNm = "Radiology3"}
                };

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.PatientRadiologyOrders).Returns(data.Object);

                var mockAppConfiRepo = new Mock<IApplicationConfigurationRepository>();
                this.patientRepository = new PatientRepository(mockContext.Object, mockAppConfiRepo.Object);

                // Act
                var result = await this.patientRepository.GetPatientRadiologyReports(mockEncounters);

                // Assert
                for (var i = 0; i < result.Count; i++)
                {
                    Assert.AreEqual(comparator[i].PatientEncounterId, result[i].PatientEncounterId);
                }

                Assert.AreEqual(comparator.Count, result.Count);
            }
        }
    }
}
