namespace SafetySurveillance.WebAPI.UnitTests.Repositories
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using MockQueryable.Moq;
    using Moq;
    using SafetySurveillanceWebApi.Data;
    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Repositories;

    [TestClass]
    public class HealthSystemRepositoryTests
    {
        private HealthSystemRepository healthSystemRepository;

        [TestInitialize]
        public void SetUp()
        {
        }

        [TestClass]
        public class GetHealthSystemForDomain : HealthSystemRepositoryTests
        {
            [ExpectedException(typeof(ArgumentNullException))]
            [TestMethod]
            public void ShouldThrowExceptionIfDomainIsNull()
            {
                // Arrange
                var data = new List<HealthSystem>
                               {
                                   new HealthSystem(),
                                   new HealthSystem()
                               }
                    .AsQueryable()
                    .BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.HealthSystems).Returns(data.Object);

                this.healthSystemRepository = new HealthSystemRepository(mockContext.Object);

                // Act
                var result = this.healthSystemRepository.GetHealthSystemForDomain(null);

                // Assert
                // expect exception
            }

            [TestMethod]
            public void ShouldReturnAppropriateHealthSystemWhenGivenDomain()
            {
                // Arrange
                var data = new List<HealthSystem>
                               {
                                   new HealthSystem()
                                       {
                                           Domain = "TestDomain1"
                                       },
                                   new HealthSystem()
                                       {
                                           Domain = "TestDomain2"
                                       }
                               }
                    .AsQueryable()
                    .BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.HealthSystems).Returns(data.Object);

                var comparator = new HealthSystem() { Domain = "TestDomain1" };

                this.healthSystemRepository = new HealthSystemRepository(mockContext.Object);

                // Act
                var result = this.healthSystemRepository.GetHealthSystemForDomain("TestDomain1");

                // Assert
                Assert.AreEqual(comparator.Domain, result.Domain);
            }
        }
    }
}
