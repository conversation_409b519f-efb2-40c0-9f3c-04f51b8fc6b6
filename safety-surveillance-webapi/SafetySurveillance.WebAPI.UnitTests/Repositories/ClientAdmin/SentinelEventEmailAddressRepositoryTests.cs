namespace SafetySurveillance.WebAPI.UnitTests.Repositories.ClientAdmin
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using MockQueryable.Moq;
    using Moq;
    using SafetySurveillanceWebApi.Data;
    using SafetySurveillanceWebApi.Exceptions;
    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Models.ClientAdmin.CustomLocations;
    using SafetySurveillanceWebApi.Models.ClientAdmin.SentinelEventEmailAddresses;
    using SafetySurveillanceWebApi.Models.Survey;
    using SafetySurveillanceWebApi.Repositories;
    using SafetySurveillanceWebApi.Repositories.ClientAdmin;

    [TestClass]
    public class SentinelEventEmailAddressRepositoryTests
    {
        private SentinelEventEmailAddressRepository sentinelEventEmailAddressRepository;

        [TestInitialize]
        public void SetUp()
        {
        }

        [TestClass]
        public class AddSentinelEventEmailAddressesAsync : SentinelEventEmailAddressRepositoryTests
        {
            [ExpectedException(typeof(NullReferenceException))]
            [TestMethod]
            public async Task ShouldThrowExceptionWhenUserIdNull()
            {
                // Arrange
                // Act
                await sentinelEventEmailAddressRepository.AddSentinelEventEmailAddressesAsync(new AddSentinelEventEmailAddressesInputDTO()
                {
                    Contacts = new List<SentinelEventContact>()
                    {
                        new SentinelEventContact()
                        {
                            Email = "<EMAIL>",
                            FirstName = "John",
                            LastName = "Doe"
                        }
                    },
                    Facility = null
                }, "test");

                // Assert
                // expect exception
            }

            [TestMethod]
            public async Task ShouldAddAndReturnPropertiesAccordingly()
            {
                // Arrange
                var testFacility = "Facility1";
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                var result = await sentinelEventEmailAddressRepository.AddSentinelEventEmailAddressesAsync(new AddSentinelEventEmailAddressesInputDTO()
                {
                    Facility = testFacility,
                    Contacts = new List<SentinelEventContact>()
                    {
                        new SentinelEventContact()
                        {
                            Email = "<EMAIL>",
                            FirstName = "Mister",
                            LastName = "Doctor"
                        },
                        new SentinelEventContact()
                        {
                            Email = "<EMAIL>",
                            FirstName = "I'm a dupe",
                            LastName = "such duplication"
                        }
                    }
                },
                "User1");

                // Assert
                Assert.AreEqual(result.AddedEmailAddresses.Count, 1);
                Assert.AreEqual(result.DuplicateEmailAddresses.Count, 1);
            }

            [TestMethod]
            public async Task ShouldNotAddAnyLocationsWhenNonePassValidations()
            {
                // Arrange
                var testFacility = "Facility1";
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                var result = await sentinelEventEmailAddressRepository.AddSentinelEventEmailAddressesAsync(new AddSentinelEventEmailAddressesInputDTO()
                {
                    Facility = testFacility,
                    Contacts = new List<SentinelEventContact>()
                    {
                        new SentinelEventContact()
                        {
                            Email = "<EMAIL>",
                            FirstName = "Duplicate",
                            LastName = "One"
                        },
                        new SentinelEventContact()
                        {
                            Email = "<EMAIL>",
                            FirstName = "I'm a dupe",
                            LastName = "such duplication"
                        }
                    }
                },
                "User1");

                // Assert
                Assert.AreEqual(result.AddedEmailAddresses.Count, 0);
                Assert.AreEqual(result.DuplicateEmailAddresses.Count, 2);
            }

            [TestMethod]
            public async Task ShouldReturnNamesWithErrorsAsTheyWereProvided()
            {
                // Arrange
                var testFacility = "Facility1";
                var testCaseSensitiveEmail = "<EMAIL>";
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                var result = await sentinelEventEmailAddressRepository.AddSentinelEventEmailAddressesAsync(new AddSentinelEventEmailAddressesInputDTO()
                {
                    Facility = testFacility,
                    Contacts = new List<SentinelEventContact>()
                    {
                        new SentinelEventContact()
                        {
                            Email = testCaseSensitiveEmail,
                            FirstName = "Case",
                            LastName = "Checking"
                        }
                    }
                },
                "User1");

                // Assert
                Assert.AreEqual(result.DuplicateEmailAddresses.Count, 1);
                Assert.AreEqual(result.DuplicateEmailAddresses.ToList()[0], testCaseSensitiveEmail);
            }
        }

        [TestClass]
        public class GetSentinelEventEmailAddressesByFacilityAsync : SentinelEventEmailAddressRepositoryTests
        {
            [ExpectedException(typeof(NullReferenceException))]
            [TestMethod]
            public async Task ShouldThrowExceptionWhenUserIdNull()
            {
                // Arrange
                // Act
                await sentinelEventEmailAddressRepository.GetSentinelEventEmailAddressesByFacilityAsync(null);

                // Assert
            }

            [TestMethod]
            public async Task ShouldReturnActiveRecordsForTheGivenFacility()
            {
                var testFacility = "Facility1";
                // Arrange
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                var result = await sentinelEventEmailAddressRepository.GetSentinelEventEmailAddressesByFacilityAsync(testFacility);

                // Assert
                Assert.AreEqual(result.Count, 2);
            }

            [TestMethod]
            public async Task ShouldReturnLocationsWhenOneOrMoreExistForTheProvidedFacilityThatAreNotDeleted()
            {
                var testFacility = "Facility1";
                // Arrange
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = true
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                var result = await sentinelEventEmailAddressRepository.GetSentinelEventEmailAddressesByFacilityAsync(testFacility);

                // Assert
                Assert.AreEqual(result.Count, 1);
            }

            [TestMethod]
            public async Task ShouldReturnNoLocationsWhenNoneMeetTheCriteria()
            {
                var testFacility = "Facility1";
                // Arrange
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = "NotTheRightFacility",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = true
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                var result = await sentinelEventEmailAddressRepository.GetSentinelEventEmailAddressesByFacilityAsync(testFacility);

                // Assert
                Assert.AreEqual(result.Count, 0);
            }
        }

        [TestClass]
        public class UpdateSentinelEventEmailAddressAsync : SentinelEventEmailAddressRepositoryTests
        {
            [ExpectedException(typeof(NullReferenceException))]
            [TestMethod]
            public async Task ShouldThrowExceptionWhenNoRecordFound()
            {
                // Arrange
                // Act
                await sentinelEventEmailAddressRepository.UpdateSentinelEventEmailAddressAsync(new UpdateSentinelEventEmailAddressInputDTO()
                {
                    Id = Guid.NewGuid(),
                    Facility = "facility",
                    Email = "<EMAIL>",
                    FirstName = "John",
                    LastName= "Doe"
                });

                // Assert
                // expect exception
            }

            [ExpectedException(typeof(ConflictException))]
            [TestMethod]
            public async Task ShouldThrowconflictExceptionWhenDuplicateIsFoundForAnEmailAddress()
            {
                // Arrange
                var testId = Guid.NewGuid();
                var duplicateEmail = "<EMAIL>";
                var testFacility = "Facility1";
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = duplicateEmail,
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = testId,
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                await sentinelEventEmailAddressRepository.UpdateSentinelEventEmailAddressAsync(new UpdateSentinelEventEmailAddressInputDTO()
                {
                    Id = testId,
                    Email = duplicateEmail,
                    FirstName = "John",
                    LastName = "Doe",
                    Facility = testFacility
                });
            }

            [TestMethod]
            public async Task ShouldUpdateWhenNoDuplicatesFound()
            {
                // Arrange
                var id = Guid.NewGuid();
                var email = "<EMAIL>";
                var firstName = "John";
                var lastName = "Doe";
                var testFacility = "Facility1";
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = id,
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = "Facility2",
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                await sentinelEventEmailAddressRepository.UpdateSentinelEventEmailAddressAsync(new UpdateSentinelEventEmailAddressInputDTO()
                {
                    Id = id,
                    Facility = testFacility,
                    Email = email,
                    FirstName = firstName,
                    LastName = lastName
                });

                var updatedRecord = await sentinelEventEmailAddressRepository.GetSingleSentinelEventEmailAddressById(id, testFacility);

                // Assert
                Assert.AreEqual(updatedRecord.Email, email);
                Assert.AreEqual(updatedRecord.FirstName, firstName);
                Assert.AreEqual(updatedRecord.LastName, lastName);
            }
        }

        [TestClass]
        public class DeleteSentinelEventEmailAddressAsync : SentinelEventEmailAddressRepositoryTests
        {
            [ExpectedException(typeof(NullReferenceException))]
            [TestMethod]
            public async Task ShouldThrowExceptionWhenNoRecordFound()
            {
                // Arrange
                // Act
                await sentinelEventEmailAddressRepository.DeleteSentinelEventEmailAddressAsync(Guid.NewGuid(), "facility");

                // Assert
                // expect exception
            }

            [TestMethod]
            public async Task ShouldDeleteWhenRecordFound()
            {
                // Arrange
                var id = Guid.NewGuid();
                var testFacility = "Facility1";
                var emails = new List<SentinelEventEmailAddress>
                               {
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = id,
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "Jane",
                                       LastName = "Doe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   },
                                   new SentinelEventEmailAddress()
                                   {
                                       Id = Guid.NewGuid(),
                                       Email = "<EMAIL>",
                                       FirstName = "John",
                                       LastName = "Schmoe",
                                       Facility = testFacility,
                                       AddedOn = DateTime.Now,
                                       UpdatedOn = DateTime.Now,
                                       AddedBy = "User1",
                                       IsDeleted = false
                                   }
                               };
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();

                var data = emails
                                .AsQueryable()
                                .BuildMockDbSet();

                mockContext.SetupGet(c => c.SentinelEventEmailAddresses).Returns(data.Object);

                sentinelEventEmailAddressRepository = new SentinelEventEmailAddressRepository(mockContext.Object);

                // Act
                await sentinelEventEmailAddressRepository.DeleteSentinelEventEmailAddressAsync(id, testFacility);

                var updatedRecord = await sentinelEventEmailAddressRepository.GetSingleSentinelEventEmailAddressById(id, testFacility);
                var activeRecords = await sentinelEventEmailAddressRepository.GetSentinelEventEmailAddressesByFacilityAsync(testFacility);

                // Assert
                Assert.IsTrue(updatedRecord.IsDeleted);
                Assert.AreEqual(activeRecords.Count, 2);
            }
        }
    }
}
