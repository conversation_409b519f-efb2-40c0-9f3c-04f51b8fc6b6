//namespace SafetySurveillance.WebAPI.UnitTests.Repositories
//{
//    using System;
//    using System.Collections.Generic;
//    using System.Linq;
//    using System.Threading.Tasks;
//    using Microsoft.VisualStudio.TestTools.UnitTesting;
//    using MockQueryable.Moq;
//    using Moq;
//    using SafetySurveillanceWebApi.Data;
//    using SafetySurveillanceWebApi.Extensions;
//    using SafetySurveillanceWebApi.Models;
//    using SafetySurveillanceWebApi.Models.Common;
//    using SafetySurveillanceWebApi.Models.Constants;
//    using SafetySurveillanceWebApi.Models.Dtos;
//    using SafetySurveillanceWebApi.Models.Dtos.AdverseEventFilters;
//    using SafetySurveillanceWebApi.Models.Dtos.Filters;
//    using SafetySurveillanceWebApi.Repositories;
//    using SafetySurveillanceWebApi.Services;

//    [TestClass]
//    public class TriggerEventRepositoryTests
//    {
//        private TriggerEventRepository triggerEventRepository;
//        private TriggerSearchService triggerSearchService;
//        private Mock<ITriggerEventCommentRepository> mockCommentRepo;

//        [TestInitialize]
//        public void SetUp()
//        {
//           this.mockCommentRepo = new Mock<ITriggerEventCommentRepository>();
//        }

//        public void SetupComment(List<TriggerDetail> details)
//        {
//            this.mockCommentRepo.Setup(m => m.MatchTriggerCommentsAsync(details))
//                .Returns(Task.FromResult(details));
//        }

//        [TestClass]
//        public class GetFaciltiesAsync : TriggerEventRepositoryTests
//        {
//            [TestMethod]
//            public async Task ShouldReturnAListOfFacilities()
//            {
//                // Arrange
//                var data = new List<CommonEncounter>
//                               {
//                                   new CommonEncounter() { Location = "Facility1" },
//                                   new CommonEncounter() { Location = "Facility2" },
//                                   new CommonEncounter() { Location = "Facility3" },
//                                   new CommonEncounter() { Location = "Facility3" }
//                               }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();
               
//                mockContext.SetupGet(c => c.CommonEncounters).Returns(data.Object);

//                var comparator = new List<string> { "Facility1", "Facility2", "Facility3" };

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);

//                // Act
//                var result = await this.triggerEventRepository.GetFacilitiesAsync();

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i], result[i]);
//                }

//                Assert.AreEqual(comparator.Count, result.Count);
//            }
//        }

//        [TestClass]
//        public class GetTriggerCategoriesAsync : TriggerEventRepositoryTests
//        {
//            [Ignore]
//            [TestMethod]
//            public async Task ShouldReturnCorrectListOfTriggerCategories()
//            {
//                // Arrange
//                var triggerDetailData = new List<TriggerDetail>
//                                            {
//                                                new TriggerDetail() { TriggerId = "1" },
//                                                new TriggerDetail() { TriggerId = "2" },
//                                                new TriggerDetail() { TriggerId = "3" }
//                                            }.AsQueryable().BuildMockDbSet();

//                var triggerReferenceData = new List<TriggerReference>
//                               {
//                                   new TriggerReference() { TriggerId = "1", TriggerCategory = "Category1" },
//                                   new TriggerReference() { TriggerId = "2", TriggerCategory = "Category2" },
//                                   new TriggerReference() { TriggerId = "3", TriggerCategory = "Category3" }
//                               }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                var mockTriggerIDs = new string[] { "1", "2" };

//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);
//                mockContext.SetupGet(c => c.TriggerReferences).Returns(triggerReferenceData.Object);
//                mockAppConfigRepo.Setup(a => a.GetSelectedTriggerIds()).Returns(Task.FromResult(mockTriggerIDs));

//                var comparator = new List<string> { "Category1", "Category2" };

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerCategoriesAsync(SurveillanceTypeConstants.Harm);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i], result[i]);
//                }

//                Assert.AreEqual(comparator.Count, result.Count);
//            }
//        }

//        [TestClass]
//        public class GetTriggerEventAsync : TriggerEventRepositoryTests
//        {
//            [ExpectedException(typeof(ArgumentNullException))]
//            [TestMethod]
//            public async Task ShouldThrowExceptionIfTriggerSourceDataIdNull()
//            {
//                // Arrange
//                var triggerDetailData = new List<TriggerDetail>
//                                            {
//                                                new TriggerDetail() { TriggerId = "1", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "2", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "3", PatientEncounterId = "1234", TriggerSourceDataId = "4567" }
//                                            }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventAsync("1", "1234", null);

//                // Assert
//                // expect exception
//            }

//            [ExpectedException(typeof(ArgumentNullException))]
//            [TestMethod]
//            public async Task ShouldThrowExceptionIfPatientEncounterIdNull()
//            {
//                // Arrange
//                var triggerDetailData = new List<TriggerDetail>
//                                            {
//                                                new TriggerDetail() { TriggerId = "1", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "2", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "3", PatientEncounterId = "1234", TriggerSourceDataId = "4567" }
//                                            }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventAsync("1", null, "4567");

//                // Assert
//                // expect exception
//            }

//            [ExpectedException(typeof(ArgumentNullException))]
//            [TestMethod]
//            public async Task ShouldThrowExceptionIfTriggerIdNull()
//            {
//                // Arrange
//                var triggerDetailData = new List<TriggerDetail>
//                                            {
//                                                new TriggerDetail() { TriggerId = "1", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "2", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "3", PatientEncounterId = "1234", TriggerSourceDataId = "4567" }
//                                            }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventAsync(null, "1234", "4567");

//                // Assert
//                // expect exception
//            }

//            [TestMethod]
//            public async Task ShouldReturnCorrectTriggerDetail()
//            {
//                // Arrange
//                var triggerDetailData = new List<TriggerDetail>
//                                            {
//                                                new TriggerDetail() { TriggerId = "1", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "2", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },
//                                                new TriggerDetail() { TriggerId = "3", PatientEncounterId = "1234", TriggerSourceDataId = "4567" }
//                                            }.AsQueryable().BuildMockDbSet();

//                var historicalTriggerDetailData = new List<HistoricalTriggerDetail>
//                {
//                    new HistoricalTriggerDetail() { TriggerId = "1", PatientEncounterId = "1234", TriggerSourceDataId = "4567" },

//                }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);
//                mockContext.SetupGet(c => c.HistoricalTriggerDetails).Returns(historicalTriggerDetailData.Object);

//                var comparator = new TriggerDetail()
//                                     {
//                                         TriggerId = "1", PatientEncounterId = "1234", TriggerSourceDataId = "4567"
//                                     };
//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);

//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventAsync("1", "1234", "4567");

//                // Assert
//                Assert.AreEqual(comparator.TriggerId, result.TriggerId);
//                Assert.AreEqual(comparator.PatientEncounterId, result.PatientEncounterId);
//                Assert.AreEqual(comparator.TriggerSourceDataId, result.TriggerSourceDataId);
//            }
//        }

//        [TestClass]
//        public class GetTriggerEventsForPatientEncounter : TriggerEventRepositoryTests
//        {
//            [ExpectedException(typeof(ArgumentNullException))]
//            [TestMethod]
//            public void ShouldThrowExceptionIfPatientEncounterIdNull()
//            {
//                // Arrange
//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = this.triggerEventRepository.GetTriggerEventsForPatientEncounter(null, SurveillanceTypeEnum.Harm.ToStringEnum());

//                // Assert
//                // expect exception
//            }

//            [Ignore]
//            [TestMethod]
//            public void ShouldReturnEnumerableOfTriggerDetail()
//            {
//                //TODO: Yeah...gotta figure this one out...
//                // Arrange
//                var mockPatientWatches = new List<PatientWatch>() { new PatientWatch() { } };
//                var mockTriggerStatuses = new List<TriggerStatus>() { new TriggerStatus() { } };
//                var mockTriggerEventAssignment = new List<TriggerEventAssignment>() { new TriggerEventAssignment() };
//                var triggerDetailData = new List<TriggerDetail>
//                                            {
//                                                new TriggerDetail() { TriggerId = "1", PatientEncounterId = "1234", TriggerSourceDataId = "4567", PatientWatch = mockPatientWatches },
//                                                new TriggerDetail() { TriggerId = "2", PatientEncounterId = "1234", TriggerSourceDataId = "4567", PatientWatch = mockPatientWatches },
//                                                new TriggerDetail() { TriggerId = "3", PatientEncounterId = "1234", TriggerSourceDataId = "4567", PatientWatch = mockPatientWatches }
//                                            }.AsQueryable().BuildMockDbSet();

//                var patientWatchData = new List<PatientWatch>()
//                                           {
//                                               new PatientWatch() { Mrn = "1234" },
//                                               new PatientWatch() { Mrn = "4567" },
//                                               new PatientWatch() { Mrn = "7891" },
//                                           }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                var mockTriggerIDs = new string[] { "1", "2" };

//                var comparator = new List<TriggerDetail>()
//                                     {
//                                         new TriggerDetail()
//                                             {
//                                                 TriggerId = "1",
//                                                 PatientEncounterId = "1234",
//                                                 TriggerSourceDataId = "4567",
//                                                 PatientWatch = mockPatientWatches
//                                             }
//                                     };

//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);
//                mockContext.SetupGet(sc => sc.PatientWatches).Returns(patientWatchData.Object);
//                mockTriggerReviewRepo.Setup(r => r.GetTriggerStatusesAsync()).Returns(Task.FromResult(mockTriggerStatuses));
//                mockTriggerReviewRepo.Setup(r => r.GetTriggerEventAssignmentsAsync()).Returns(Task.FromResult(mockTriggerEventAssignment));
//                mockAppConfigRepo.Setup(a => a.GetSelectedTriggerIds()).Returns(Task.FromResult(mockTriggerIDs));


//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = this.triggerEventRepository.GetTriggerEventsForPatientEncounter("1234", SurveillanceTypeEnum.Harm.ToStringEnum());

//                // Assert
//                Assert.IsInstanceOfType(result, typeof(IEnumerable<TriggerDetail>));
//            }
//        }

//        [TestClass]
//        public class GetTriggerEventsWithReviewsAsync : TriggerEventRepositoryTests
//        {
//            [ExpectedException(typeof(ArgumentNullException))]
//            [TestMethod]
//            public async Task ShouldThrowExceptionIsFacilitiesNull()
//            {
//                // Arrange
//                var mockFacilities = new List<string>() {"Facility1", "Facility2"};
//                var mockStartDate = new DateTime(2018, 10, 10);
//                var mockEndDate = new DateTime(2018, 10, 18);
//                var mockFilterData = new AdverseEventFilterData();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result =
//                    await this.triggerEventRepository.GetTriggerEventsWithReviewsAsync(null, mockStartDate, mockEndDate,SurveillanceTypeConstants.Harm,
//                        mockFilterData, null);

//                // Assert
//                // expect exception
//            }

//            [ExpectedException(typeof(ArgumentException))]
//            [TestMethod]
//            public async Task ShouldThrowExceptionIfStartDateBeforeEndDate()
//            {
//                // Arrange
//                var mockFacilities = new List<string>() {"Facility1", "Facility2"};
//                var mockStartDate = new DateTime(2018, 10, 18);
//                var mockEndDate = new DateTime(2018, 10, 10);
//                var mockFilterData = new AdverseEventFilterData();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventsWithReviewsAsync(mockFacilities,
//                    mockStartDate, mockEndDate,SurveillanceTypeConstants.Harm, mockFilterData, null);

//                // Assert
//                // expect exception
//            }

//            [Ignore]
//            [TestMethod]
//            public async Task ShouldReturnCorrectListOfTriggerDetailWhenGivenParameters()
//            {
//                // Arrange
//                var mockFacilities = new List<string>() {"Facility1", "Facility2"};
//                var mockStartDate = new DateTime(2018, 10, 10);
//                var mockEndDate = new DateTime(2018, 10, 18);
//                var mockFilterData = new AdverseEventFilterData();

//                new List<PatientWatch>() {new PatientWatch() { }};
//                var mockTriggerStatuses = new List<TriggerStatus>() {new TriggerStatus() { }};
//                var mockTriggerEventAssignment = new List<TriggerEventAssignment>() {new TriggerEventAssignment()};

//                var list = new List<TriggerDetail>
//                {
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 12),
//                        Encounter = new CommonEncounter() {Location = "Facility1"}
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "2", TriggerDts = new DateTime(2018, 10, 14),
//                        Encounter = new CommonEncounter() {Location = "Facility2"}
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "3", TriggerDts = new DateTime(2018, 10, 20),
//                        Encounter = new CommonEncounter() {Location = "Facility2"}
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "4", TriggerDts = new DateTime(2018, 10, 13),
//                        Encounter = new CommonEncounter() {Location = "Facility3"}
//                    },
//                };
//                var triggerDetailData = list
//                    .AsQueryable()
//                    .BuildMockDbSet();

//                var patientWatchData = new List<PatientWatch>()
//                {
//                    new PatientWatch() {Mrn = "1234"},
//                    new PatientWatch() {Mrn = "4567"},
//                    new PatientWatch() {Mrn = "7891"},
//                }.AsQueryable().BuildMockDbSet();

//                var triggerReferenceData = new List<TriggerReference>
//                {
//                    new TriggerReference() {TriggerId = "1", TriggerCategory = "Category1"},
//                    new TriggerReference() {TriggerId = "2", TriggerCategory = "Category2"},
//                    new TriggerReference() {TriggerId = "3", TriggerCategory = "Category3"}
//                }.AsQueryable().BuildMockDbSet();

//                var encounterData = new List<CommonEncounter>()
//                {
//                    new CommonEncounter() {Location = "Facility1"},
//                    new CommonEncounter() {Location = "Facility2"},
//                    new CommonEncounter() {Location = "Facility3"}
//                }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                var mockTriggerIDs = new string[] {"1", "2"};

//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);
//                mockContext.SetupGet(c => c.CommonEncounters).Returns(encounterData.Object);
//                mockContext.SetupGet(c => c.TriggerReferences).Returns(triggerReferenceData.Object);
//                mockContext.SetupGet(sc => sc.PatientWatches).Returns(patientWatchData.Object);

//                mockTriggerReviewRepo.Setup(r => r.GetTriggerStatusesAsync())
//                    .Returns(Task.FromResult(mockTriggerStatuses));
//                mockTriggerReviewRepo.Setup(r => r.GetTriggerEventAssignmentsAsync())
//                    .Returns(Task.FromResult(mockTriggerEventAssignment));
//                mockAppConfigRepo.Setup(a => a.GetSelectedTriggerIds()).Returns(Task.FromResult(mockTriggerIDs));


//                var comparator = new List<TriggerDetail>
//                {
//                    list[0],
//                    list[1]
//                };

//                mockAdverseEventFilterService.Setup(f => f.FilterTriggerResults(It.IsAny<List<TriggerDetail>>(), It.IsAny<AdverseEventFilterData>()))
//                    .Returns(comparator);

//                this.SetupComment(comparator);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventsWithReviewsAsync(mockFacilities,
//                    mockStartDate, mockEndDate,SurveillanceTypeConstants.Harm, mockFilterData, null);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i].TriggerId, result[i].TriggerId);
//                    Assert.AreEqual(comparator[i].TriggerDts, result[i].TriggerDts);
//                    Assert.AreEqual(comparator[i].Encounter.Location, result[i].Encounter.Location);
//                }

//                Assert.AreEqual(comparator.Count, result.Count);
//            }


//            [Ignore]
//            [TestMethod]
//            public async Task ShouldReturnCorrectListOfTriggerDetailWhenGivenParametersAndAeDateFlag()
//            {
//                // Arrange
//                var mockFacilities = new List<string>() {"Facility1", "Facility2"};
//                var mockStartDate = new DateTime(2018, 10, 20);
//                var mockEndDate = new DateTime(2018, 10, 25);
//                var mockFilterData = new AdverseEventFilterData();

//                var mockPatientWatches = new List<PatientWatch>() {new PatientWatch() { }};
//                var mockTriggerStatuses = new List<TriggerStatus>() {new TriggerStatus() { }};
//                var mockTriggerEventAssignment = new List<TriggerEventAssignment>() {new TriggerEventAssignment()};

//                var list = new List<TriggerDetail>
//                {
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 21),
//                        Encounter = new CommonEncounter() {Location = "Facility1"},
//                        TriggerEventReview = new TriggerEventReview() {AdverseEventDts = new DateTime(2018, 10, 22)}
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "2", TriggerDts = new DateTime(2018, 10, 22),
//                        Encounter = new CommonEncounter() {Location = "Facility2"},
//                        TriggerEventReview = new TriggerEventReview() {AdverseEventDts = new DateTime(2018, 10, 26)}
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "3", TriggerDts = new DateTime(2018, 10, 23),
//                        Encounter = new CommonEncounter() {Location = "Facility2"},
//                        TriggerEventReview = new TriggerEventReview() {AdverseEventDts = new DateTime(2018, 10, 27)}
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "4", TriggerDts = new DateTime(2018, 10, 24),
//                        Encounter = new CommonEncounter() {Location = "Facility3"},
//                        TriggerEventReview = new TriggerEventReview() {AdverseEventDts = new DateTime(2018, 10, 28)}
//                    }
//                };
//                var triggerDetailData = list
//                    .AsQueryable()
//                    .BuildMockDbSet();

//                var patientWatchData = new List<PatientWatch>()
//                {
//                    new PatientWatch() {Mrn = "1234"},
//                    new PatientWatch() {Mrn = "4567"},
//                    new PatientWatch() {Mrn = "7891"},
//                }.AsQueryable().BuildMockDbSet();

//                var triggerReferenceData = new List<TriggerReference>
//                {
//                    new TriggerReference() {TriggerId = "1", TriggerCategory = "Category1"},
//                    new TriggerReference() {TriggerId = "2", TriggerCategory = "Category2"},
//                    new TriggerReference() {TriggerId = "3", TriggerCategory = "Category3"}
//                }.AsQueryable().BuildMockDbSet();

//                var encounterData = new List<CommonEncounter>()
//                {
//                    new CommonEncounter() {Location = "Facility1"},
//                    new CommonEncounter() {Location = "Facility2"},
//                    new CommonEncounter() {Location = "Facility3"}
//                }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();

//                var mockTriggerIDs = new string[] {"1", "2"};

//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);
//                mockContext.SetupGet(c => c.CommonEncounters).Returns(encounterData.Object);
//                mockContext.SetupGet(c => c.TriggerReferences).Returns(triggerReferenceData.Object);
//                mockContext.SetupGet(sc => sc.PatientWatches).Returns(patientWatchData.Object);
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                mockTriggerReviewRepo.Setup(r => r.GetTriggerStatusesAsync())
//                    .Returns(Task.FromResult(mockTriggerStatuses));
//                mockTriggerReviewRepo.Setup(r => r.GetTriggerEventAssignmentsAsync())
//                    .Returns(Task.FromResult(mockTriggerEventAssignment));
//                mockAppConfigRepo.Setup(a => a.GetSelectedTriggerIds()).Returns(Task.FromResult(mockTriggerIDs));

//                var comparator = new List<TriggerDetail>
//                {
//                    list[0]
//                };

//                mockAdverseEventFilterService
//                    .Setup(f => f.FilterTriggerResults(It.IsAny<List<TriggerDetail>>(), It.IsAny<AdverseEventFilterData>()))
//                    .Returns(comparator);

//                this.SetupComment(comparator);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventsWithReviewsAsync(mockFacilities,
//                    mockStartDate, mockEndDate,SurveillanceTypeConstants.Harm, mockFilterData, null, false, true);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i].TriggerId, result[i].TriggerId);
//                    Assert.AreEqual(comparator[i].TriggerDts, result[i].TriggerDts);
//                    Assert.AreEqual(comparator[i].Encounter.Location, result[i].Encounter.Location);
//                }

//                Assert.AreEqual(comparator.Count, result.Count);
//            }

//            [Ignore]
//            [TestMethod]
//            public async Task ReturnsUniqueAeWhenFlagSetTrue()
//            {
//                // Arrange
//                var mockFacilities = new List<string>() {"Facility1", "Facility2"};
//                var mockStartDate = new DateTime(2018, 10, 20);
//                var mockEndDate = new DateTime(2018, 10, 25);
//                var mockFilterData = new AdverseEventFilterData();
//                var mockTriggerEventReview = new TriggerEventReview()
//                {
//                    ReviewId = 1234,
//                    AdverseEventDts = new DateTime(2018, 10, 22)
//                };
//                var mockEncounter = new CommonEncounter()
//                {
//                    EncounterId = "123ValIsAwesome",
//                    Location = "Facility1"
//                };

//                var mockTriggerStatuses = new List<TriggerStatus>() {new TriggerStatus() { }};
//                var mockTriggerEventAssignment = new List<TriggerEventAssignment>() {new TriggerEventAssignment()};
//                var mockTriggerEventReviews = new List<TriggerEventReview>()
//                {
//                    mockTriggerEventReview
//                }.AsQueryable().BuildMockDbSet();

//                var list = new List<TriggerDetail>
//                {
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 21),
//                        TriggerSourceDataId = "Number1",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 22),
//                        TriggerSourceDataId = "Number2",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 23),
//                        TriggerSourceDataId = "Number3",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 24),
//                        TriggerSourceDataId = "Number4",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    }
//                };
//                var triggerDetailData = list
//                    .AsQueryable()
//                    .BuildMockDbSet();

//                var patientWatchData = new List<PatientWatch>()
//                {
//                    new PatientWatch() {Mrn = "1234"},
//                    new PatientWatch() {Mrn = "4567"},
//                    new PatientWatch() {Mrn = "7891"},
//                }.AsQueryable().BuildMockDbSet();

//                var triggerReferenceData = new List<TriggerReference>
//                {
//                    new TriggerReference() {TriggerId = "1", TriggerCategory = "Category1"},
//                    new TriggerReference() {TriggerId = "2", TriggerCategory = "Category2"},
//                    new TriggerReference() {TriggerId = "3", TriggerCategory = "Category3"}
//                }.AsQueryable().BuildMockDbSet();

//                var encounterData = new List<CommonEncounter>()
//                {
//                    new CommonEncounter() {Location = "Facility1"},
//                    new CommonEncounter() {Location = "Facility2"},
//                    new CommonEncounter() {Location = "Facility3"}
//                }.AsQueryable().BuildMockDbSet();

//                var triggerEventToReviewData = new List<TriggerEventToReview>()
//                {
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number1"
//                    },
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number2"
//                    },
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number3"
//                    },
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number4"
//                    }
//                }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();

//                var mockTriggerIDs = new string[] {"1", "2"};

//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);
//                mockContext.SetupGet(c => c.CommonEncounters).Returns(encounterData.Object);
//                mockContext.SetupGet(c => c.TriggerReferences).Returns(triggerReferenceData.Object);
//                mockContext.SetupGet(sc => sc.PatientWatches).Returns(patientWatchData.Object);
//                mockContext.SetupGet(c => c.TriggerEventToReviews).Returns(triggerEventToReviewData.Object);
//                mockContext.SetupGet(c => c.TriggerEventReviews).Returns(mockTriggerEventReviews.Object);
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                mockTriggerReviewRepo.Setup(r => r.GetTriggerStatusesAsync())
//                    .Returns(Task.FromResult(mockTriggerStatuses));
//                mockTriggerReviewRepo.Setup(r => r.GetTriggerEventAssignmentsAsync())
//                    .Returns(Task.FromResult(mockTriggerEventAssignment));
//                mockAppConfigRepo.Setup(a => a.GetSelectedTriggerIds()).Returns(Task.FromResult(mockTriggerIDs));

//                var comparator = new List<TriggerDetail>
//                {
//                    list[0],
//                    list[1],
//                    list[2],
//                    list[3],
//                };

//                mockAdverseEventFilterService
//                    .Setup(f => f.FilterTriggerResults(It.IsAny<List<TriggerDetail>>(), It.IsAny<AdverseEventFilterData>()))
//                    .Returns(comparator);

//                mockTriggerMatchService.Setup(ms => ms.ShowUniqueReviewsOnly(It.IsAny<List<TriggerDetail>>()))
//                    .Returns(comparator);

//                this.SetupComment(comparator);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventsWithReviewsAsync(mockFacilities,
//                    mockStartDate, mockEndDate,SurveillanceTypeConstants.Harm, mockFilterData, null, false, true, true);

//                // Assert
//                mockTriggerMatchService.Verify(f => f.ShowUniqueReviewsOnly(list), Times.Once);
//            }

//            [Ignore]
//            [TestMethod]
//            public async Task ReturnsNotUniqueAeWhenFlagSetFalse()
//            {
//                // Arrange
//                var mockFacilities = new List<string>() { "Facility1", "Facility2" };
//                var mockStartDate = new DateTime(2018, 10, 20);
//                var mockEndDate = new DateTime(2018, 10, 25);
//                var mockFilterData = new AdverseEventFilterData();
//                var mockTriggerEventReview = new TriggerEventReview()
//                {
//                    ReviewId = 1234,
//                    AdverseEventDts = new DateTime(2018, 10, 22)
//                };
//                var mockEncounter = new CommonEncounter()
//                {
//                    EncounterId = "123ValIsAwesome",
//                    Location = "Facility1"
//                };

//                var mockTriggerStatuses = new List<TriggerStatus>() { new TriggerStatus() { } };
//                var mockTriggerEventAssignment = new List<TriggerEventAssignment>() { new TriggerEventAssignment() };
//                var mockTriggerEventReviews = new List<TriggerEventReview>()
//                {
//                    mockTriggerEventReview
//                }.AsQueryable().BuildMockDbSet();

//                var list = new List<TriggerDetail>
//                {
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 21),
//                        TriggerSourceDataId = "Number1",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 22),
//                        TriggerSourceDataId = "Number2",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 23),
//                        TriggerSourceDataId = "Number3",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    },
//                    new TriggerDetail()
//                    {
//                        TriggerId = "1", TriggerDts = new DateTime(2018, 10, 24),
//                        TriggerSourceDataId = "Number4",
//                        Encounter = mockEncounter,
//                        TriggerEventReview = mockTriggerEventReview
//                    }
//                };
//                var triggerDetailData = list
//                    .AsQueryable()
//                    .BuildMockDbSet();

//                var patientWatchData = new List<PatientWatch>()
//                {
//                    new PatientWatch() {Mrn = "1234"},
//                    new PatientWatch() {Mrn = "4567"},
//                    new PatientWatch() {Mrn = "7891"},
//                }.AsQueryable().BuildMockDbSet();

//                var triggerReferenceData = new List<TriggerReference>
//                {
//                    new TriggerReference() {TriggerId = "1", TriggerCategory = "Category1"},
//                    new TriggerReference() {TriggerId = "2", TriggerCategory = "Category2"},
//                    new TriggerReference() {TriggerId = "3", TriggerCategory = "Category3"}
//                }.AsQueryable().BuildMockDbSet();

//                var encounterData = new List<CommonEncounter>()
//                {
//                    new CommonEncounter() {Location = "Facility1"},
//                    new CommonEncounter() {Location = "Facility2"},
//                    new CommonEncounter() {Location = "Facility3"}
//                }.AsQueryable().BuildMockDbSet();

//                var triggerEventToReviewData = new List<TriggerEventToReview>()
//                {
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number1"
//                    },
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number2"
//                    },
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number3"
//                    },
//                    new TriggerEventToReview()
//                    {
//                        PatientEncounterId = mockEncounter.EncounterId,
//                        TriggerEventReview = mockTriggerEventReview,
//                        TriggerId = "1",
//                        TriggerReviewId = mockTriggerEventReview.ReviewId,
//                        TriggerSourceDataId = "Number4"
//                    }
//                }.AsQueryable().BuildMockDbSet();

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();

//                var mockTriggerIDs = new string[] { "1", "2" };

//                mockContext.SetupGet(c => c.TriggerEvents).Returns(triggerDetailData.Object);
//                mockContext.SetupGet(c => c.CommonEncounters).Returns(encounterData.Object);
//                mockContext.SetupGet(c => c.TriggerReferences).Returns(triggerReferenceData.Object);
//                mockContext.SetupGet(sc => sc.PatientWatches).Returns(patientWatchData.Object);
//                mockContext.SetupGet(c => c.TriggerEventToReviews).Returns(triggerEventToReviewData.Object);
//                mockContext.SetupGet(c => c.TriggerEventReviews).Returns(mockTriggerEventReviews.Object);
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                mockTriggerReviewRepo.Setup(r => r.GetTriggerStatusesAsync())
//                    .Returns(Task.FromResult(mockTriggerStatuses));
//                mockTriggerReviewRepo.Setup(r => r.GetTriggerEventAssignmentsAsync())
//                    .Returns(Task.FromResult(mockTriggerEventAssignment));
//                mockAppConfigRepo.Setup(a => a.GetSelectedTriggerIds()).Returns(Task.FromResult(mockTriggerIDs));

//                var comparator = new List<TriggerDetail>
//                {
//                    list[0],
//                    list[1],
//                    list[2],
//                    list[3],
//                };

//                mockAdverseEventFilterService
//                    .Setup(f => f.FilterTriggerResults(It.IsAny<List<TriggerDetail>>(), It.IsAny<AdverseEventFilterData>()))
//                    .Returns(comparator);

//                this.SetupComment(comparator);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);


//                // Act
//                var result = await this.triggerEventRepository.GetTriggerEventsWithReviewsAsync(mockFacilities,
//                    mockStartDate, mockEndDate, SurveillanceTypeConstants.Harm, mockFilterData, null, false, true, false);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i].TriggerId, result[i].TriggerId);
//                    Assert.AreEqual(comparator[i].TriggerDts, result[i].TriggerDts);
//                    Assert.AreEqual(comparator[i].Encounter.Location, result[i].Encounter.Location);
//                }

//                Assert.AreEqual(comparator.Count, result.Count);
//            }
//        }

//        [TestClass]
//        public class FilterSubCategories : TriggerEventRepositoryTests
//        {

//            [TestMethod]
//            public void ShouldReturnTriggerEventsWhenGivenAppropriateParameters()
//            {
//                // Arrange
//                var mockFilterData = new AdverseEventFilterData()
//                     {
//                        CategoryData = new List<CategoryData>()
//                                           {
//                                                new CategoryData()
//                                                    {
//                                                        Subcategories = new List<SubcategoryData>()
//                                                                            {
//                                                                                new SubcategoryData()
//                                                                                    {
//                                                                                        name = "SubCategory1",
//                                                                                        value = 1
//                                                                                    }
//                                                                            }
//                                                    }
//                                           }
//                     };
//                var list = new List<TriggerDetail>
//                               {
//                                   new TriggerDetail() { TriggerId = "1", TriggerEventReview = new TriggerEventReview() { AdverseEventSubCategoryId = 1 } },
//                                   new TriggerDetail() { TriggerId = "2", TriggerEventReview = new TriggerEventReview() { AdverseEventSubCategoryId = 1 } },
//                                   new TriggerDetail() { TriggerId = "3", TriggerEventReview = new TriggerEventReview() { AdverseEventSubCategoryId = 3 } },
//                                   new TriggerDetail() { TriggerId = "4", TriggerEventReview = new TriggerEventReview() { AdverseEventSubCategoryId = 4 } }
//                               };

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                var comparator = new List<TriggerDetail> { list[0], list[1] };

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);

//                this.triggerSearchService = new TriggerSearchService();


//                // Act
//                var result = this.triggerSearchService.FilterSubCategories(list, mockFilterData);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i].TriggerId, result[i].TriggerId);
//                }
//                Assert.AreEqual(comparator.Count, result.Count);
//            }
//        }

//        [TestClass]
//        public class FilterServiceLine : TriggerEventRepositoryTests
//        {
//            [TestMethod]
//            public void ShouldReturnTriggerEventsWhenGivenAppropriateParameters()
//            {
//                // Arrange
//                var mockFilterData = new AdverseEventFilterData()
//                {
//                    AssociatedServiceLine = new List<FilterModel>()
//                                      {
//                                          new FilterModel()
//                                              {
//                                                  Name = "ServiceLine1",
//                                                  Value = "ServiceLine1"
//                                              }
//                                      }
//                };
//                var list = new List<TriggerDetail>
//                       {
//                           new TriggerDetail() { TriggerId = "1", TriggerEventReview = new TriggerEventReview() { AssociatedServiceLines = new List<AssociatedServiceLine>() {new AssociatedServiceLine() {ServiceLine = "ServiceLine1"}}}},
//                           new TriggerDetail() { TriggerId = "2", TriggerEventReview = new TriggerEventReview() { AssociatedServiceLines = new List<AssociatedServiceLine>() {new AssociatedServiceLine() {ServiceLine = "ServiceLine1"}}}},
//                           new TriggerDetail() { TriggerId = "3", TriggerEventReview = new TriggerEventReview() { AssociatedServiceLines = new List<AssociatedServiceLine>() {new AssociatedServiceLine() {ServiceLine = "ServiceLine2"}}}},
//                           new TriggerDetail() { TriggerId = "4", TriggerEventReview = new TriggerEventReview() { AssociatedServiceLines = new List<AssociatedServiceLine>() {new AssociatedServiceLine() {ServiceLine = "ServiceLine3"}}}},

//                       };

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                var comparator = new List<TriggerDetail> { list[0], list[1] };

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);

//                this.triggerSearchService = new TriggerSearchService();

//                // Act
//                var result = this.triggerSearchService.FilterServiceLine(list, mockFilterData);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i].TriggerId, result[i].TriggerId);
//                }
//                Assert.AreEqual(comparator.Count, result.Count);
//            }
//        }

//        [TestClass]
//        public class FilterSeverity : TriggerEventRepositoryTests
//        {
//            [TestMethod]
//            public void ShouldReturnTriggerEventsWhenGivenAppropriateParameters()
//            {
//                // Arrange
//                var mockFilterData = new AdverseEventFilterData()
//                {
//                    Severity = new List<FilterModel>()
//                      {
//                          new FilterModel()
//                              {
//                                  Name = "A",
//                                  Value = "A"
//                              }
//                      }
//                };
//                var list = new List<TriggerDetail>
//                   {
//                       new TriggerDetail() { TriggerId = "1", TriggerEventReview = new TriggerEventReview() { Severity = "A" } },
//                       new TriggerDetail() { TriggerId = "2", TriggerEventReview = new TriggerEventReview() { Severity = "A" } },
//                       new TriggerDetail() { TriggerId = "3", TriggerEventReview = new TriggerEventReview() { Severity = "B" } },
//                       new TriggerDetail() { TriggerId = "4", TriggerEventReview = new TriggerEventReview() { Severity = "C" } }
//                   };

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                var comparator = new List<TriggerDetail> { list[0], list[1] };

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);

//                this.triggerSearchService = new TriggerSearchService();

//                // Act
//                var result = this.triggerSearchService.FilterSeverity(list, mockFilterData);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i].TriggerId, result[i].TriggerId);
//                }

//                Assert.AreEqual(comparator.Count, result.Count);
//            }
//        }

//        [TestClass]
//        public class FilterUnits : TriggerEventRepositoryTests
//        {
//            [TestMethod]
//            public void ShouldReturnTriggerEventsWhenGivenAppropriateParameters()
//            {
//                // Arrange
//                var mockFilterData = new AdverseEventFilterData()
//                {
//                    AssociatedUnit = new List<FilterModel>()
//                      {
//                          new FilterModel()
//                              {
//                                  Value = "Unit1",
//                                  Name = "Unit1"
//                              }
//                      }
//                };
//                var list = new List<TriggerDetail>
//                               {
//                                   new TriggerDetail() { TriggerId = "1", TriggerEventReview = new TriggerEventReview() { AssociatedUnits = new List<AssociatedUnit>()
//                                   {
//                                       new AssociatedUnit() { Unit = new UnitReference(){UnitId = "Unit1"}}
//                                   }} },
//                                   new TriggerDetail() { TriggerId = "2", TriggerEventReview =  new TriggerEventReview() { AssociatedUnits = new List<AssociatedUnit>()
//                                   {
//                                       new AssociatedUnit() { Unit = new UnitReference(){UnitId = "Unit1"}}
//                                   }} },
//                                   new TriggerDetail() { TriggerId = "3", TriggerEventReview =  new TriggerEventReview() { AssociatedUnits = new List<AssociatedUnit>()
//                                   {
//                                       new AssociatedUnit() { Unit = new UnitReference(){UnitId = "Unit2"}}
//                                   }} },
//                                   new TriggerDetail() { TriggerId = "4", TriggerEventReview = new TriggerEventReview() { AssociatedUnits = new List<AssociatedUnit>()
//                                   {
//                                       new AssociatedUnit() { Unit = new UnitReference(){UnitId = "Unit3"}}
//                                   }} },
//                               };

//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();


//                var comparator = new List<TriggerDetail> { list[0], list[1] };

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);

//                this.triggerSearchService = new TriggerSearchService();

//                // Act
//                var result = this.triggerSearchService.FilterUnits(list, mockFilterData);

//                // Assert
//                for (var i = 0; i < result.Count; i++)
//                {
//                    Assert.AreEqual(comparator[i].TriggerId, result[i].TriggerId);
//                }

//                Assert.AreEqual(comparator.Count, result.Count);
//            }
//        }

//        [TestClass]
//        public class GetTriggerReferencesAsync : TriggerEventRepositoryTests
//        {
//            [TestMethod]
//            public void ShouldReturnAllTriggerReferences()
//            {
//                // Arrange
//                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
//                var mockTriggerReviewRepo = new Mock<ITriggerReviewRepository>();
//                var mockAdverseEventFilterService = new Mock<IAdverseEventFilterService>();
//                var mockAppConfigRepo = new Mock<IApplicationConfigurationRepository>();
//                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
//                var mockTriggerSearchService = new Mock<ITriggerSearchService>();

//                var triggerReferencesList = new List<TriggerReference>
//                {
//                    new TriggerReference
//                    {
//                        TriggerId = "1", TriggerName = "Test 1", TriggerCategory = "Category 1", TriggerType = "Type 1"
//                    },
//                    new TriggerReference
//                    {
//                        TriggerId = "2", TriggerName = "Test 2", TriggerCategory = "Category 2", TriggerType = "Type 2"
//                    },
//                    new TriggerReference
//                    {
//                        TriggerId = "3", TriggerName = "Test 31", TriggerCategory = "Category 3", TriggerType = "Type 3"
//                    }
//                };
//                var triggerReferences = triggerReferencesList
//                    .AsQueryable()
//                    .BuildMockDbSet();

//                mockContext
//                    .Setup(c => c.TriggerReferences)
//                    .Returns(triggerReferences.Object);

//                this.triggerEventRepository = new TriggerEventRepository(
//                    mockContext.Object,
//                    mockTriggerReviewRepo.Object,
//                    mockAdverseEventFilterService.Object,
//                    mockAppConfigRepo.Object,
//                    mockTriggerMatchService.Object,
//                    mockTriggerSearchService.Object,
//                    this.mockCommentRepo.Object);

//                // Act
//                var result = this.triggerEventRepository.GetTriggerReferencesAsync();

//                // Assert
//                Assert.IsNotNull(result.Result);
//                Assert.AreEqual(triggerReferencesList.Count, result.Result.Count);
//            }

//        }
//    }
//}
