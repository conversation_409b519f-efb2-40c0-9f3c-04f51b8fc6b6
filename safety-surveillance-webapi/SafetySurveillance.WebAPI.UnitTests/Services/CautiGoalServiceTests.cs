using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Repositories;
using SafetySurveillanceWebApi.Services;
using System.Collections.Generic;
using System.Threading.Tasks;
using SafetySurveillanceWebApi.Models.Constants;

namespace SafetySurveillance.WebAPI.UnitTests.Services
{
    [TestClass]
    public class CautiGoalServiceTests
    {
        private const string element1 = "ele1";
        private const string element2 = "ele2";
        private const string element3 = "ele3";

        private readonly List<CautiBundle> testBundles = new List<CautiBundle> {
            new CautiBundle { BundleType = BundleType.Insert, ElementId = element1, Goal = .1m },
            new CautiBundle { BundleType = BundleType.Insert, ElementId = element2, Goal = .3m },
            new CautiBundle { BundleType = BundleType.Insert, ElementId = element3, Goal = .8m }
        };

        [TestMethod]
        public async Task GetBundleGoals_ReturnsGoalsById()
        {
            // Arrange
            var repositoryMock = new Mock<ICautiRepository>();
            repositoryMock.Setup(r => r.GetBundlesById("", ""))
                .Returns(Task.FromResult(testBundles));
            var service = new CautiGoalService(repositoryMock.Object);

            // Act
            var goals = await service.GetBundleGoals("", "");

            // Assert
            CollectionAssert.AreEquivalent(new List<string> { element1, element2, element3 }, goals.Keys);
            Assert.AreEqual(.1m, goals[element1]);
            Assert.AreEqual(.3m, goals[element2]);
            Assert.AreEqual(.8m, goals[element3]);
        }

        [TestMethod]
        public async Task GetBundleGoalsWithOverall_ReturnsGoalInfo()
        {
            // Arrange
            var repositoryMock = new Mock<ICautiRepository>();
            repositoryMock.Setup(r => r.GetBundlesById(BundleType.Insert, ""))
                .Returns(Task.FromResult(testBundles));
            var service = new CautiGoalService(repositoryMock.Object);

            // Act
            var goals = await service.GetBundleGoalsWithOverall(BundleType.Insert);

            // Assert
            Assert.AreEqual(BundleType.Insert, goals.BundleType);
            Assert.AreEqual(goals.OverallGoal, .4m);
            CollectionAssert.AreEquivalent(new List<string> { element1, element2, element3 },
                goals.BundleElementGoals.Keys);
            Assert.AreEqual(.1m, goals.BundleElementGoals[element1]);
            Assert.AreEqual(.3m, goals.BundleElementGoals[element2]);
            Assert.AreEqual(.8m, goals.BundleElementGoals[element3]);
        }

        [TestMethod]
        public async Task GetBundleGoal_ReturnsAverageGoal()
        {
            // Arrange
            var repositoryMock = new Mock<ICautiRepository>();
            repositoryMock.Setup(r => r.GetBundlesById("", ""))
                .Returns(Task.FromResult(testBundles));
            var service = new CautiGoalService(repositoryMock.Object);

            // Act
            var goal = await service.GetBundleGoal("", "");

            // Assert
            Assert.AreEqual(.4m, goal);
        }

        [TestMethod]
        public async Task GetBundleGoal_ReturnsSingleGoal()
        {
            // Arrange
            var repositoryMock = new Mock<ICautiRepository>();
            repositoryMock.Setup(r => r.GetBundlesById(null, null))
                .Returns(Task.FromResult(new List<CautiBundle> { new CautiBundle {
                    BundleType = BundleType.Insert, ElementId = element1, Goal = .5m } }));
            var service = new CautiGoalService(repositoryMock.Object);

            // Act
            var goal = await service.GetBundleGoal(null, null);

            // Assert
            Assert.AreEqual(.5m, goal);
        }
    }
}
